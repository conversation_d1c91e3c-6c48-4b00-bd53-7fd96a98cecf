// =================================================================
// ==                  Jewelry Workshop Backend - Complete       ==
// =================================================================
// Author: Enhanced Version
// Description: A complete Node.js server using Express.js to manage
// a jewelry workshop's data with MySQL database (converted from PostgreSQL).
// Features: Full CRUD API for all modules, transactions, and interactive API
// documentation with Swagger.
// =================================================================

// Import required packages
const express = require('express');
const cors = require('cors');
const path = require('path');
const mysql = require('mysql2/promise');
const swaggerJSDoc = require('swagger-jsdoc');
const swaggerUi = require('swagger-ui-express');
require('dotenv').config();

// Create an Express application
const app = express();
const port = 3000;

// Middleware
app.use(cors());
app.use(express.json());

// MySQL Database Connection Pool (converted from PostgreSQL)
const pool = mysql.createPool({
  host: 'localhost',
  user: 'root',
  database: 'black_diamond_jewelry',
  password: '2452329511',
  port: 3306,
  waitForConnections: true,
  connectionLimit: 10,
  queueLimit: 0
});

// Test database connection
async function testConnection() {
  try {
    const connection = await pool.getConnection();
    console.log('Connected to MySQL database successfully!');
    connection.release();
  } catch (err) {
    console.error('Error acquiring database connection:', err.message);
  }
}

testConnection();

// Swagger Configuration
const swaggerOptions = {
  swaggerDefinition: {
    openapi: '3.0.0',
    info: {
      title: 'Jewelry Workshop API - Complete',
      version: '2.0.0',
      description: 'واجهة برمجة التطبيقات الكاملة لإدارة عمليات ورشة الذهب والماس',
      contact: {
        name: 'Black Diamond Jewelry Workshop',
        email: '<EMAIL>'
      }
    },
    servers: [
      {
        url: `http://localhost:${port}`,
        description: 'خادم التطوير المحلي'
      }
    ],
    tags: [
        { name: 'Workers', description: 'إدارة العمال وحساباتهم' },
        { name: 'Customers', description: 'إدارة العملاء والأرصدة' },
        { name: 'Production', description: 'إدارة مراحل الإنتاج' },
        { name: 'Inventory', description: 'إدارة المخزون والمواد الخام' },
        { name: 'Expenses', description: 'إدارة المصروفات العامة' },
        { name: 'Transactions', description: 'المعاملات المالية' },
        { name: 'Casting', description: 'عمليات السباكة' },
        { name: 'Polishing', description: 'عمليات التلميع' },
        { name: 'Filing', description: 'خسية المبرد' }
    ]
  },
  apis: ['./api/complete-server.js'], // Path to the API docs
};

const swaggerDocs = swaggerJSDoc(swaggerOptions);
app.use('/api-docs', swaggerUi.serve, swaggerUi.setup(swaggerDocs, {
  customCss: `
    .swagger-ui .topbar { display: none; }
    .swagger-ui .info .title { color: #c7a46f; }
  `,
  customSiteTitle: 'ورشة الذهب والماس - API Documentation'
}));

// =================================================================
// ==                  API Endpoints: Workers                     ==
// =================================================================

/**
 * @swagger
 * /api/workers:
 *   get:
 *     summary: جلب قائمة بكل العمال
 *     tags: [Workers]
 *     responses:
 *       200:
 *         description: قائمة العمال مع أرصدتهم
 */
app.get('/api/workers', async (req, res) => {
    try {
        const [rows] = await pool.query('SELECT * FROM workers ORDER BY name');
        res.json(rows);
    } catch (err) {
        console.error(err.message);
        res.status(500).json({ error: err.message });
    }
});

/**
 * @swagger
 * /api/workers/{id}:
 *   get:
 *     summary: جلب عامل واحد بواسطة الـ ID
 *     tags: [Workers]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *     responses:
 *       200:
 *         description: بيانات العامل
 *       404:
 *         description: العامل غير موجود
 */
app.get('/api/workers/:id', async (req, res) => {
    try {
        const { id } = req.params;
        const [rows] = await pool.query('SELECT * FROM workers WHERE id = ?', [id]);
        if (rows.length === 0) {
            return res.status(404).json({ msg: 'Worker not found' });
        }
        res.json(rows[0]);
    } catch (err) {
        console.error(err.message);
        res.status(500).json({ error: err.message });
    }
});

/**
 * @swagger
 * /api/workers:
 *   post:
 *     summary: إنشاء عامل جديد
 *     tags: [Workers]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - name
 *               - nationality
 *             properties:
 *               name:
 *                 type: string
 *                 description: اسم العامل
 *               nationality:
 *                 type: string
 *                 description: الجنسية
 *               start_date:
 *                 type: string
 *                 format: date
 *                 description: تاريخ بدء العمل
 *               salary_egp:
 *                 type: number
 *                 description: الراتب بالجنيه المصري
 *               salary_usd:
 *                 type: number
 *                 description: الراتب بالدولار الأمريكي
 *               notes:
 *                 type: string
 *                 description: ملاحظات
 *     responses:
 *       201:
 *         description: تم إنشاء العامل بنجاح
 */
app.post('/api/workers', async (req, res) => {
    const connection = await pool.getConnection();
    try {
        await connection.beginTransaction();
        
        const { name, nationality, start_date, salary_egp, salary_usd, notes } = req.body;
        
        const [result] = await connection.query(
            'INSERT INTO workers (name, nationality, start_date, salary_egp, salary_usd, notes, balance_gold, balance_egp, balance_usd) VALUES (?, ?, ?, ?, ?, ?, 0, 0, 0)',
            [name, nationality, start_date, salary_egp || 0, salary_usd || 0, notes]
        );
        
        await connection.commit();
        
        const [newWorker] = await connection.query('SELECT * FROM workers WHERE id = ?', [result.insertId]);
        res.status(201).json(newWorker[0]);
    } catch (err) {
        await connection.rollback();
        console.error(err.message);
        res.status(500).json({ error: err.message });
    } finally {
        connection.release();
    }
});

/**
 * @swagger
 * /api/workers/{id}:
 *   put:
 *     summary: تحديث بيانات عامل
 *     tags: [Workers]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *     responses:
 *       200:
 *         description: تم تحديث العامل بنجاح
 *       404:
 *         description: العامل غير موجود
 */
app.put('/api/workers/:id', async (req, res) => {
    try {
        const { id } = req.params;
        const { name, nationality, start_date, salary_egp, salary_usd, notes } = req.body;
        
        const [result] = await pool.query(
            'UPDATE workers SET name = ?, nationality = ?, start_date = ?, salary_egp = ?, salary_usd = ?, notes = ? WHERE id = ?',
            [name, nationality, start_date, salary_egp, salary_usd, notes, id]
        );
        
        if (result.affectedRows === 0) {
            return res.status(404).json({ msg: 'Worker not found' });
        }
        
        const [updatedWorker] = await pool.query('SELECT * FROM workers WHERE id = ?', [id]);
        res.json(updatedWorker[0]);
    } catch (err) {
        console.error(err.message);
        res.status(500).json({ error: err.message });
    }
});

/**
 * @swagger
 * /api/workers/{id}:
 *   delete:
 *     summary: حذف عامل
 *     tags: [Workers]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *     responses:
 *       200:
 *         description: تم حذف العامل بنجاح
 *       404:
 *         description: العامل غير موجود
 */
app.delete('/api/workers/:id', async (req, res) => {
    const connection = await pool.getConnection();
    try {
        await connection.beginTransaction();
        
        const { id } = req.params;
        
        // حذف معاملات العامل أولاً
        await connection.query('DELETE FROM worker_transactions WHERE worker_id = ?', [id]);
        
        // حذف العامل
        const [result] = await connection.query('DELETE FROM workers WHERE id = ?', [id]);
        
        if (result.affectedRows === 0) {
            await connection.rollback();
            return res.status(404).json({ msg: 'Worker not found' });
        }
        
        await connection.commit();
        res.json({ msg: 'Worker deleted successfully' });
    } catch (err) {
        await connection.rollback();
        console.error(err.message);
        res.status(500).json({ error: err.message });
    } finally {
        connection.release();
    }
});

// =================================================================
// ==                  API Endpoints: Customers                   ==
// =================================================================

/**
 * @swagger
 * /api/customers:
 *   get:
 *     summary: جلب قائمة بكل العملاء
 *     tags: [Customers]
 *     responses:
 *       200:
 *         description: قائمة العملاء مع أرصدتهم
 */
app.get('/api/customers', async (req, res) => {
    try {
        const [rows] = await pool.query('SELECT * FROM customers ORDER BY name');
        res.json(rows);
    } catch (err) {
        console.error(err.message);
        res.status(500).json({ error: err.message });
    }
});

/**
 * @swagger
 * /api/customers/{id}:
 *   get:
 *     summary: جلب عميل واحد بواسطة الـ ID
 *     tags: [Customers]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *     responses:
 *       200:
 *         description: بيانات العميل
 *       404:
 *         description: العميل غير موجود
 */
app.get('/api/customers/:id', async (req, res) => {
    try {
        const { id } = req.params;
        const [rows] = await pool.query('SELECT * FROM customers WHERE id = ?', [id]);
        if (rows.length === 0) {
            return res.status(404).json({ msg: 'Customer not found' });
        }
        res.json(rows[0]);
    } catch (err) {
        console.error(err.message);
        res.status(500).json({ error: err.message });
    }
});

/**
 * @swagger
 * /api/customers:
 *   post:
 *     summary: إنشاء عميل جديد مع أرصدة افتتاحية
 *     tags: [Customers]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - name
 *             properties:
 *               name:
 *                 type: string
 *                 description: اسم العميل
 *               contact:
 *                 type: string
 *                 description: بيانات الاتصال
 *               balance_gold:
 *                 type: number
 *                 description: رصيد الذهب (جرام)
 *               balance_labor_egp:
 *                 type: number
 *                 description: رصيد المصنعية (ج.م)
 *               balance_labor_usd:
 *                 type: number
 *                 description: رصيد المصنعية ($)
 *               balance_diamond_usd:
 *                 type: number
 *                 description: رصيد الألماس ($)
 *               balance_egyptian_gold_usd:
 *                 type: number
 *                 description: رصيد الذهب المصري ($)
 *     responses:
 *       201:
 *         description: تم إنشاء العميل وحركاته الافتتاحية بنجاح
 */
app.post('/api/customers', async (req, res) => {
    const connection = await pool.getConnection();
    try {
        await connection.beginTransaction();

        const { name, contact, balance_gold, balance_labor_egp, balance_labor_usd, balance_diamond_usd, balance_egyptian_gold_usd } = req.body;

        const [result] = await connection.query(
            'INSERT INTO customers (name, contact, balance_gold, balance_labor_egp, balance_labor_usd, balance_diamond_usd, balance_egyptian_gold_usd) VALUES (?, ?, ?, ?, ?, ?, ?)',
            [name, contact, balance_gold || 0, balance_labor_egp || 0, balance_labor_usd || 0, balance_diamond_usd || 0, balance_egyptian_gold_usd || 0]
        );

        const customerId = result.insertId;

        // إضافة معاملات الأرصدة الافتتاحية
        const openingBalances = [
            { type: 'balance_gold', amount: balance_gold, currency: 'GRAM', description: 'رصيد ذهب افتتاحي' },
            { type: 'balance_labor_egp', amount: balance_labor_egp, currency: 'EGP', description: 'رصيد مصنعية افتتاحي (ج.م)' },
            { type: 'balance_labor_usd', amount: balance_labor_usd, currency: 'USD', description: 'رصيد مصنعية افتتاحي ($)' },
            { type: 'balance_diamond_usd', amount: balance_diamond_usd, currency: 'USD', description: 'رصيد ألماس افتتاحي' },
            { type: 'balance_egyptian_gold_usd', amount: balance_egyptian_gold_usd, currency: 'USD', description: 'رصيد ذهب مصري افتتاحي' }
        ];

        for (const balance of openingBalances) {
            if (balance.amount && balance.amount !== 0) {
                await connection.query(
                    'INSERT INTO customer_transactions (customer_id, description, type, balance_type, amount, currency) VALUES (?, ?, ?, ?, ?, ?)',
                    [customerId, balance.description, 'opening_balance', balance.type, balance.amount, balance.currency]
                );
            }
        }

        await connection.commit();

        const [newCustomer] = await connection.query('SELECT * FROM customers WHERE id = ?', [customerId]);
        res.status(201).json(newCustomer[0]);
    } catch (err) {
        await connection.rollback();
        console.error(err.message);
        res.status(500).json({ error: err.message });
    } finally {
        connection.release();
    }
});

app.put('/api/customers/:id', async (req, res) => {
    try {
        const { id } = req.params;
        const { name, contact, balance_gold, balance_labor_egp, balance_labor_usd, balance_diamond_usd, balance_egyptian_gold_usd } = req.body;

        const [result] = await pool.query(
            'UPDATE customers SET name = ?, contact = ?, balance_gold = ?, balance_labor_egp = ?, balance_labor_usd = ?, balance_diamond_usd = ?, balance_egyptian_gold_usd = ? WHERE id = ?',
            [name, contact, balance_gold, balance_labor_egp, balance_labor_usd, balance_diamond_usd, balance_egyptian_gold_usd, id]
        );

        if (result.affectedRows === 0) {
            return res.status(404).json({ msg: 'Customer not found' });
        }

        const [updatedCustomer] = await pool.query('SELECT * FROM customers WHERE id = ?', [id]);
        res.json(updatedCustomer[0]);
    } catch (err) {
        console.error(err.message);
        res.status(500).json({ error: err.message });
    }
});

app.delete('/api/customers/:id', async (req, res) => {
    const connection = await pool.getConnection();
    try {
        await connection.beginTransaction();

        const { id } = req.params;

        // حذف معاملات العميل أولاً
        await connection.query('DELETE FROM customer_transactions WHERE customer_id = ?', [id]);

        // حذف العميل
        const [result] = await connection.query('DELETE FROM customers WHERE id = ?', [id]);

        if (result.affectedRows === 0) {
            await connection.rollback();
            return res.status(404).json({ msg: 'Customer not found' });
        }

        await connection.commit();
        res.json({ msg: 'Customer deleted successfully' });
    } catch (err) {
        await connection.rollback();
        console.error(err.message);
        res.status(500).json({ error: err.message });
    } finally {
        connection.release();
    }
});

// =================================================================
// ==              API Endpoints: Production Orders              ==
// =================================================================

/**
 * @swagger
 * /api/orders:
 *   get:
 *     summary: جلب كل أوامر التشغيل
 *     tags: [Production]
 *     responses:
 *       200:
 *         description: قائمة بأوامر التشغيل
 */
app.get('/api/orders', async (req, res) => {
    try {
        const [rows] = await pool.query(`
            SELECT po.*, c.name as customer_name, w.name as worker_name
            FROM production_orders po
            LEFT JOIN customers c ON po.customer_id = c.id
            LEFT JOIN workers w ON po.worker_id = w.id
            ORDER BY po.created_at DESC
        `);
        res.json(rows);
    } catch (err) {
        console.error(err.message);
        res.status(500).json({ error: err.message });
    }
});

app.get('/api/orders/:id', async (req, res) => {
    try {
        const { id } = req.params;
        const [rows] = await pool.query('SELECT * FROM production_orders WHERE id = ?', [id]);
        if (rows.length === 0) {
            return res.status(404).json({ msg: 'Order not found' });
        }
        res.json(rows[0]);
    } catch (err) {
        console.error(err.message);
        res.status(500).json({ error: err.message });
    }
});

/**
 * @swagger
 * /api/orders:
 *   post:
 *     summary: إنشاء أمر تشغيل جديد
 *     tags: [Production]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *     responses:
 *       201:
 *         description: تم إنشاء الأمر بنجاح
 */
app.post('/api/orders', async (req, res) => {
    try {
        const { customer_id, worker_id, item, description, carat, weight, quantity, notes, status } = req.body;
        const [result] = await pool.query(
            'INSERT INTO production_orders (customer_id, worker_id, item, description, carat, weight, quantity, notes, status) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)',
            [customer_id, worker_id || null, item, description, carat, weight, quantity, notes, status || 'جديد']
        );

        const [newOrder] = await pool.query('SELECT * FROM production_orders WHERE id = ?', [result.insertId]);
        res.status(201).json(newOrder[0]);
    } catch (err) {
        console.error(err.message);
        res.status(500).json({ error: err.message });
    }
});

app.put('/api/orders/:id', async (req, res) => {
    try {
        const { id } = req.params;
        const { customer_id, worker_id, item, description, carat, weight, quantity, notes, status } = req.body;

        const [result] = await pool.query(
            'UPDATE production_orders SET customer_id = ?, worker_id = ?, item = ?, description = ?, carat = ?, weight = ?, quantity = ?, notes = ?, status = ? WHERE id = ?',
            [customer_id, worker_id, item, description, carat, weight, quantity, notes, status, id]
        );

        if (result.affectedRows === 0) {
            return res.status(404).json({ msg: 'Order not found' });
        }

        const [updatedOrder] = await pool.query('SELECT * FROM production_orders WHERE id = ?', [id]);
        res.json(updatedOrder[0]);
    } catch (err) {
        console.error(err.message);
        res.status(500).json({ error: err.message });
    }
});

app.delete('/api/orders/:id', async (req, res) => {
    try {
        const { id } = req.params;
        const [result] = await pool.query('DELETE FROM production_orders WHERE id = ?', [id]);

        if (result.affectedRows === 0) {
            return res.status(404).json({ msg: 'Order not found' });
        }

        res.json({ msg: 'Order deleted successfully' });
    } catch (err) {
        console.error(err.message);
        res.status(500).json({ error: err.message });
    }
});

// =================================================================
// ==               API Endpoints: Inventory                      ==
// =================================================================

/**
 * @swagger
 * /api/inventory:
 *   get:
 *     summary: جلب كل المواد الخام
 *     tags: [Inventory]
 *     responses:
 *       200:
 *         description: قائمة المواد الخام
 */
app.get('/api/inventory', async (req, res) => {
    try {
        const [rows] = await pool.query('SELECT * FROM raw_materials ORDER BY name');
        res.json(rows);
    } catch (err) {
        console.error(err.message);
        res.status(500).json({ error: err.message });
    }
});

app.get('/api/inventory/:id', async (req, res) => {
    try {
        const { id } = req.params;
        const [rows] = await pool.query('SELECT * FROM raw_materials WHERE id = ?', [id]);
        if (rows.length === 0) {
            return res.status(404).json({ msg: 'Material not found' });
        }
        res.json(rows[0]);
    } catch (err) {
        console.error(err.message);
        res.status(500).json({ error: err.message });
    }
});

/**
 * @swagger
 * /api/inventory:
 *   post:
 *     summary: إضافة مادة خام جديدة
 *     tags: [Inventory]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *     responses:
 *       201:
 *         description: تم إضافة المادة بنجاح
 */
app.post('/api/inventory', async (req, res) => {
    try {
        const { code, name, unit, stock } = req.body;
        const [result] = await pool.query(
            'INSERT INTO raw_materials (code, name, unit, stock) VALUES (?, ?, ?, ?)',
            [code, name, unit, stock || 0]
        );

        const [newMaterial] = await pool.query('SELECT * FROM raw_materials WHERE id = ?', [result.insertId]);
        res.status(201).json(newMaterial[0]);
    } catch (err) {
        console.error(err.message);
        res.status(500).json({ error: err.message });
    }
});

app.put('/api/inventory/:id', async (req, res) => {
    try {
        const { id } = req.params;
        const { code, name, unit, stock } = req.body;

        const [result] = await pool.query(
            'UPDATE raw_materials SET code = ?, name = ?, unit = ?, stock = ? WHERE id = ?',
            [code, name, unit, stock, id]
        );

        if (result.affectedRows === 0) {
            return res.status(404).json({ msg: 'Material not found' });
        }

        const [updatedMaterial] = await pool.query('SELECT * FROM raw_materials WHERE id = ?', [id]);
        res.json(updatedMaterial[0]);
    } catch (err) {
        console.error(err.message);
        res.status(500).json({ error: err.message });
    }
});

app.delete('/api/inventory/:id', async (req, res) => {
    try {
        const { id } = req.params;
        const [result] = await pool.query('DELETE FROM raw_materials WHERE id = ?', [id]);

        if (result.affectedRows === 0) {
            return res.status(404).json({ msg: 'Material not found' });
        }

        res.json({ msg: 'Material deleted successfully' });
    } catch (err) {
        console.error(err.message);
        res.status(500).json({ error: err.message });
    }
});

// =================================================================
// ==               API Endpoints: Expenses                       ==
// =================================================================

/**
 * @swagger
 * /api/expenses:
 *   get:
 *     summary: جلب كل المصروفات
 *     tags: [Expenses]
 *     responses:
 *       200:
 *         description: قائمة المصروفات
 */
app.get('/api/expenses', async (req, res) => {
    try {
        const [rows] = await pool.query('SELECT * FROM general_expenses ORDER BY created_at DESC');
        res.json(rows);
    } catch (err) {
        console.error(err.message);
        res.status(500).json({ error: err.message });
    }
});

app.get('/api/expenses/:id', async (req, res) => {
    try {
        const { id } = req.params;
        const [rows] = await pool.query('SELECT * FROM general_expenses WHERE id = ?', [id]);
        if (rows.length === 0) {
            return res.status(404).json({ msg: 'Expense not found' });
        }
        res.json(rows[0]);
    } catch (err) {
        console.error(err.message);
        res.status(500).json({ error: err.message });
    }
});

/**
 * @swagger
 * /api/expenses:
 *   post:
 *     summary: إنشاء مصروف جديد
 *     tags: [Expenses]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *     responses:
 *       201:
 *         description: تم إنشاء المصروف بنجاح
 */
app.post('/api/expenses', async (req, res) => {
    try {
        const { description, amount, currency } = req.body;
        const [result] = await pool.query(
            'INSERT INTO general_expenses (description, amount, currency) VALUES (?, ?, ?)',
            [description, amount, currency || 'EGP']
        );

        const [newExpense] = await pool.query('SELECT * FROM general_expenses WHERE id = ?', [result.insertId]);
        res.status(201).json(newExpense[0]);
    } catch (err) {
        console.error(err.message);
        res.status(500).json({ error: err.message });
    }
});

app.put('/api/expenses/:id', async (req, res) => {
    try {
        const { id } = req.params;
        const { description, amount, currency } = req.body;

        const [result] = await pool.query(
            'UPDATE general_expenses SET description = ?, amount = ?, currency = ? WHERE id = ?',
            [description, amount, currency, id]
        );

        if (result.affectedRows === 0) {
            return res.status(404).json({ msg: 'Expense not found' });
        }

        const [updatedExpense] = await pool.query('SELECT * FROM general_expenses WHERE id = ?', [id]);
        res.json(updatedExpense[0]);
    } catch (err) {
        console.error(err.message);
        res.status(500).json({ error: err.message });
    }
});

app.delete('/api/expenses/:id', async (req, res) => {
    try {
        const { id } = req.params;
        const [result] = await pool.query('DELETE FROM general_expenses WHERE id = ?', [id]);

        if (result.affectedRows === 0) {
            return res.status(404).json({ msg: 'Expense not found' });
        }

        res.json({ msg: 'Expense deleted successfully' });
    } catch (err) {
        console.error(err.message);
        res.status(500).json({ error: err.message });
    }
});

// =================================================================
// ==               API Endpoints: Casting Jobs                   ==
// =================================================================

/**
 * @swagger
 * /api/casting-jobs:
 *   get:
 *     summary: جلب كل أعمال السباكة
 *     tags: [Casting]
 *     responses:
 *       200:
 *         description: قائمة أعمال السباكة
 */
app.get('/api/casting-jobs', async (req, res) => {
    try {
        const [rows] = await pool.query(`
            SELECT cj.*, po.item, c.name as customer_name, w.name as worker_name
            FROM casting_jobs cj
            LEFT JOIN production_orders po ON cj.order_id = po.id
            LEFT JOIN customers c ON po.customer_id = c.id
            LEFT JOIN workers w ON cj.worker_id = w.id
            ORDER BY cj.created_at DESC
        `);
        res.json(rows);
    } catch (err) {
        console.error(err.message);
        res.status(500).json({ error: err.message });
    }
});

app.post('/api/casting-jobs', async (req, res) => {
    try {
        const { order_id, worker_id, gold_weight, gold_carat, notes, status } = req.body;
        const [result] = await pool.query(
            'INSERT INTO casting_jobs (order_id, worker_id, gold_weight, gold_carat, notes, status) VALUES (?, ?, ?, ?, ?, ?)',
            [order_id, worker_id, gold_weight, gold_carat, notes, status || 'جديد']
        );

        const [newJob] = await pool.query('SELECT * FROM casting_jobs WHERE id = ?', [result.insertId]);
        res.status(201).json(newJob[0]);
    } catch (err) {
        console.error(err.message);
        res.status(500).json({ error: err.message });
    }
});

// =================================================================
// ==               API Endpoints: Polishing Jobs                 ==
// =================================================================

/**
 * @swagger
 * /api/polishing-jobs:
 *   get:
 *     summary: جلب كل أعمال التلميع
 *     tags: [Polishing]
 *     responses:
 *       200:
 *         description: قائمة أعمال التلميع
 */
app.get('/api/polishing-jobs', async (req, res) => {
    try {
        const [rows] = await pool.query(`
            SELECT pj.*, po.item, c.name as customer_name, w.name as worker_name
            FROM polishing_jobs pj
            LEFT JOIN production_orders po ON pj.order_id = po.id
            LEFT JOIN customers c ON po.customer_id = c.id
            LEFT JOIN workers w ON pj.worker_id = w.id
            ORDER BY pj.created_at DESC
        `);
        res.json(rows);
    } catch (err) {
        console.error(err.message);
        res.status(500).json({ error: err.message });
    }
});

app.post('/api/polishing-jobs', async (req, res) => {
    try {
        const { order_id, worker_id, weight_before, weight_after, notes, status } = req.body;
        const [result] = await pool.query(
            'INSERT INTO polishing_jobs (order_id, worker_id, weight_before, weight_after, notes, status) VALUES (?, ?, ?, ?, ?, ?)',
            [order_id, worker_id, weight_before, weight_after, notes, status || 'جديد']
        );

        const [newJob] = await pool.query('SELECT * FROM polishing_jobs WHERE id = ?', [result.insertId]);
        res.status(201).json(newJob[0]);
    } catch (err) {
        console.error(err.message);
        res.status(500).json({ error: err.message });
    }
});

// =================================================================
// ==               API Endpoints: Filing Loss Jobs              ==
// =================================================================

/**
 * @swagger
 * /api/filing-loss-jobs:
 *   get:
 *     summary: جلب كل أعمال خسية المبرد
 *     tags: [Filing]
 *     responses:
 *       200:
 *         description: قائمة أعمال خسية المبرد
 */
app.get('/api/filing-loss-jobs', async (req, res) => {
    try {
        const [rows] = await pool.query(`
            SELECT flj.*, po.item, c.name as customer_name, w.name as worker_name
            FROM filing_loss_jobs flj
            LEFT JOIN production_orders po ON flj.order_id = po.id
            LEFT JOIN customers c ON po.customer_id = c.id
            LEFT JOIN workers w ON flj.worker_id = w.id
            ORDER BY flj.created_at DESC
        `);
        res.json(rows);
    } catch (err) {
        console.error(err.message);
        res.status(500).json({ error: err.message });
    }
});

app.post('/api/filing-loss-jobs', async (req, res) => {
    try {
        const { order_id, worker_id, weight_before, weight_after, loss_amount, notes, status } = req.body;
        const [result] = await pool.query(
            'INSERT INTO filing_loss_jobs (order_id, worker_id, weight_before, weight_after, loss_amount, notes, status) VALUES (?, ?, ?, ?, ?, ?, ?)',
            [order_id, worker_id, weight_before, weight_after, loss_amount, notes, status || 'جديد']
        );

        const [newJob] = await pool.query('SELECT * FROM filing_loss_jobs WHERE id = ?', [result.insertId]);
        res.status(201).json(newJob[0]);
    } catch (err) {
        console.error(err.message);
        res.status(500).json({ error: err.message });
    }
});

// =================================================================
// ==               API Endpoints: Transactions                   ==
// =================================================================

/**
 * @swagger
 * /api/workers/{id}/transactions:
 *   get:
 *     summary: جلب معاملات عامل معين
 *     tags: [Transactions]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *     responses:
 *       200:
 *         description: قائمة معاملات العامل
 */
app.get('/api/workers/:id/transactions', async (req, res) => {
    try {
        const { id } = req.params;
        const [rows] = await pool.query(
            'SELECT * FROM worker_transactions WHERE worker_id = ? ORDER BY created_at DESC',
            [id]
        );
        res.json(rows);
    } catch (err) {
        console.error(err.message);
        res.status(500).json({ error: err.message });
    }
});

/**
 * @swagger
 * /api/workers/{id}/transactions:
 *   post:
 *     summary: إضافة معاملة جديدة لعامل
 *     tags: [Transactions]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *     responses:
 *       201:
 *         description: تم إضافة المعاملة وتحديث الرصيد
 */
app.post('/api/workers/:id/transactions', async (req, res) => {
    const connection = await pool.getConnection();
    try {
        await connection.beginTransaction();

        const { id } = req.params;
        const { description, type, amount_gold, amount_egp, amount_usd } = req.body;

        // إضافة المعاملة
        const [result] = await connection.query(
            'INSERT INTO worker_transactions (worker_id, description, type, amount_gold, amount_egp, amount_usd) VALUES (?, ?, ?, ?, ?, ?)',
            [id, description, type, amount_gold || 0, amount_egp || 0, amount_usd || 0]
        );

        // تحديث رصيد العامل
        const multiplier = type === 'debit' ? 1 : -1;

        if (amount_gold) {
            await connection.query(
                'UPDATE workers SET balance_gold = balance_gold + ? WHERE id = ?',
                [amount_gold * multiplier, id]
            );
        }

        if (amount_egp) {
            await connection.query(
                'UPDATE workers SET balance_egp = balance_egp + ? WHERE id = ?',
                [amount_egp * multiplier, id]
            );
        }

        if (amount_usd) {
            await connection.query(
                'UPDATE workers SET balance_usd = balance_usd + ? WHERE id = ?',
                [amount_usd * multiplier, id]
            );
        }

        await connection.commit();

        const [transaction] = await connection.query(
            'SELECT * FROM worker_transactions WHERE id = ?',
            [result.insertId]
        );

        res.status(201).json(transaction[0]);
    } catch (err) {
        await connection.rollback();
        console.error(err.message);
        res.status(500).json({ error: err.message });
    } finally {
        connection.release();
    }
});

/**
 * @swagger
 * /api/customers/{id}/transactions:
 *   get:
 *     summary: جلب معاملات عميل معين
 *     tags: [Transactions]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *     responses:
 *       200:
 *         description: قائمة معاملات العميل
 */
app.get('/api/customers/:id/transactions', async (req, res) => {
    try {
        const { id } = req.params;
        const [rows] = await pool.query(
            'SELECT * FROM customer_transactions WHERE customer_id = ? ORDER BY created_at DESC',
            [id]
        );
        res.json(rows);
    } catch (err) {
        console.error(err.message);
        res.status(500).json({ error: err.message });
    }
});

app.post('/api/customers/:id/transactions', async (req, res) => {
    const connection = await pool.getConnection();
    try {
        await connection.beginTransaction();

        const { id } = req.params;
        const { description, type, balance_type, amount, currency } = req.body;

        // إضافة المعاملة
        const [result] = await connection.query(
            'INSERT INTO customer_transactions (customer_id, description, type, balance_type, amount, currency) VALUES (?, ?, ?, ?, ?, ?)',
            [id, description, type, balance_type, amount, currency]
        );

        // تحديث رصيد العميل
        const multiplier = type === 'debit' ? 1 : -1;
        const updateAmount = amount * multiplier;

        await connection.query(
            `UPDATE customers SET ${balance_type} = ${balance_type} + ? WHERE id = ?`,
            [updateAmount, id]
        );

        await connection.commit();

        const [transaction] = await connection.query(
            'SELECT * FROM customer_transactions WHERE id = ?',
            [result.insertId]
        );

        res.status(201).json(transaction[0]);
    } catch (err) {
        await connection.rollback();
        console.error(err.message);
        res.status(500).json({ error: err.message });
    } finally {
        connection.release();
    }
});

// =================================================================
// ==                  Static Files & Routes                     ==
// =================================================================

// Serve static files (after API routes)
app.use(express.static(path.join(__dirname, '..')));
app.use('/js', express.static(path.join(__dirname, '..', 'js')));
app.use('/assets', express.static(path.join(__dirname, '..', 'assets')));

// Serve main application page
app.get('/', (req, res) => {
    res.sendFile(path.join(__dirname, '..', 'index-mysql.html'));
});

app.get('/app', (req, res) => {
    res.sendFile(path.join(__dirname, '..', 'index-mysql.html'));
});

// =================================================================
// ==                  Database Info                             ==
// =================================================================

app.get('/api/tables', async (req, res) => {
    try {
        const [rows] = await pool.query('SHOW TABLES');
        res.json(rows.map(row => Object.values(row)[0]));
    } catch (err) {
        console.error(err.message);
        res.status(500).json({ error: err.message });
    }
});

app.get('/api/status', async (req, res) => {
    try {
        const [workers] = await pool.query('SELECT COUNT(*) as count FROM workers');
        const [customers] = await pool.query('SELECT COUNT(*) as count FROM customers');
        const [orders] = await pool.query('SELECT COUNT(*) as count FROM production_orders');
        const [expenses] = await pool.query('SELECT COUNT(*) as count FROM general_expenses');

        res.json({
            status: 'connected',
            database: 'black_diamond_jewelry',
            counts: {
                workers: workers[0].count,
                customers: customers[0].count,
                orders: orders[0].count,
                expenses: expenses[0].count
            }
        });
    } catch (err) {
        console.error(err.message);
        res.status(500).json({ error: err.message });
    }
});

// Start the server
app.listen(port, () => {
  console.log(`Complete Jewelry Workshop Server running on port ${port}`);
  console.log(`API Documentation: http://localhost:${port}/api-docs`);
  console.log(`Application: http://localhost:${port}/app`);
  console.log(`Status: http://localhost:${port}/api/status`);
});
