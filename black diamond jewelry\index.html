<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام إدارة ورشة الذهب</title>
    <!-- Chosen Palette: Goldsmith's Ledger -->
    <!-- Application Structure Plan: A single-page application (SPA) with a fixed sidebar for navigation and a main content area. This structure is intuitive for business applications, allowing the user to switch between different functional modules without reloading the page. Each module is a self-contained "view" with its own tables, forms, and interactive elements. -->
    <!-- Visualization & Content Choices: 
        - Dashboard: Goal: Inform/Analyze. Method: KPI Cards and a Bar Chart (Chart.js) for a quick business overview. Interaction: A new Gemini-powered "Analyze Performance" button takes the KPI data and generates a business summary and actionable advice.
        - Production Stages: Goal: Track Process. Expanded based on user's new files (`تلميع.xlsx`, `خسية مبرد.xlsx`). Now includes tabs for Polishing and Filing Loss, creating a comprehensive production workflow tracker from Order -> Casting -> Polishing -> Loss Assessment.
        - Inventory/Customers/Workers/Expenses: Goal: Organize. Method: Real-time interactive HTML tables powered by Firestore. Interaction: Modals for data entry.
        - Gemini Feature Enhancement: Added comprehensive monthly report generation on the dashboard and AI-powered description suggestions for various operations to standardize data entry and saving time.
    -->
    <!-- CONFIRMATION: NO SVG graphics used. NO Mermaid JS used. -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@400;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            scroll-behavior: smooth;
        }
        .chart-container {
            position: relative;
            width: 100%;
            max-width: 800px;
            margin-left: auto;
            margin-right: auto;
            height: 350px;
            max-height: 40vh;
        }
        ::-webkit-scrollbar {
            width: 8px;
        }
        ::-webkit-scrollbar-track {
            background: #f1f1f1;
        }
        ::-webkit-scrollbar-thumb {
            background: #c7a46f;
            border-radius: 10px;
        }
        ::-webkit-scrollbar-thumb:hover {
            background: #b39362;
        }
        .modal-bg {
            background-color: rgba(0, 0, 0, 0.5);
            transition: opacity 0.3s ease;
        }
        .modal-content {
            transition: transform 0.3s ease;
        }
        .loader {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #c7a46f;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
         .tab-active {
            border-color: #c7a46f;
            color: #c7a46f;
        }
        .tab-inactive {
           border-color: transparent;
           color: #6b7280;
        }
        .tab-inactive:hover {
            border-color: #d1d5db;
            color: #4b5563;
        }
    </style>
</head>
<body class="bg-[#FDFBF6] text-[#4A4A4A]">

    <div id="loading-overlay" class="fixed inset-0 bg-white bg-opacity-90 flex justify-center items-center z-50">
        <div class="loader"></div>
    </div>

    <div class="flex h-screen bg-[#FDFBF6]">
        <!-- Sidebar -->
        <aside class="w-64 bg-[#F0EBE3] p-6 shadow-lg flex flex-col justify-between overflow-y-auto">
            <div>
                <h1 class="text-2xl font-bold text-[#6B5B4B] mb-2">ورشة الذهب</h1>
                <div class="text-xs text-gray-500 mb-6">
                    <p>معرف المستخدم:</p>
                    <p id="userId" class="select-all">جار التحميل...</p>
                </div>
                <nav id="main-nav" class="space-y-3">
                    <a href="#dashboard" class="nav-link bg-[#E6DED5] text-[#554639] flex items-center p-3 rounded-lg font-bold shadow-sm">
                        <svg class="w-6 h-6 ml-3" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6"></path></svg>
                        لوحة التحكم
                    </a>
                    <a href="#production-stages" class="nav-link hover:bg-[#E6DED5] flex items-center p-3 rounded-lg transition-all duration-200">
                        <svg class="w-6 h-6 ml-3" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-2.11.732a6 6 0 01-3.86.517L6.17 15.5a2 2 0 01-1.022.547m0 0l-1.57-1.57a2 2 0 010-2.828l2.387-2.387a6 6 0 018.486 0l2.387 2.387a2 2 0 010 2.828l-1.57 1.57M9 17l.01 0"></path></svg>
                        مراحل الإنتاج
                    </a>
                    <a href="#inventory" class="nav-link hover:bg-[#E6DED5] flex items-center p-3 rounded-lg transition-all duration-200">
                        <svg class="w-6 h-6 ml-3" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 8h14M5 8a2 2 0 110-4h14a2 2 0 110 4M5 8v10a2 2 0 002 2h10a2 2 0 002-2V8m-9 4h4"></path></svg>
                        المخزون
                    </a>
                    <a href="#customers" class="nav-link hover:bg-[#E6DED5] flex items-center p-3 rounded-lg transition-all duration-200">
                        <svg class="w-6 h-6 ml-3" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path></svg>
                        العملاء
                    </a>
                     <a href="#expenses" class="nav-link hover:bg-[#E6DED5] flex items-center p-3 rounded-lg transition-all duration-200">
                        <svg class="w-6 h-6 ml-3" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 8h6m-5 4h.01M4.887 14.12l-1.396 1.396a2 2 0 000 2.828l1.396 1.396A2 2 0 006.586 20H17.414a2 2 0 001.697-.707l1.396-1.396a2 2 0 000-2.828l-1.396-1.396A2 2 0 0017.414 14H6.586a2 2 0 00-1.697.12z"></path></svg>
                        المصروفات
                    </a>
                     <a href="#workers" class="nav-link hover:bg-[#E6DED5] flex items-center p-3 rounded-lg transition-all duration-200">
                        <svg class="w-6 h-6 ml-3" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M12 12a4 4 0 110-8 4 4 0 010 8z"></path></svg>
                        العمال
                    </a>
                </nav>
            </div>
             <div class="text-center text-xs text-[#9c8b7b]">
                <p>&copy; 2025 نظام إدارة ورشة الذهب</p>
                <p>تم التطوير بواسطة مساعدك الشخصي</p>
            </div>
        </aside>

        <!-- Main Content -->
        <main class="flex-1 p-8 overflow-y-auto">
            
            <section id="dashboard" class="page-section">
                <header class="mb-8 flex justify-between items-center">
                    <div>
                        <h2 class="text-3xl font-bold text-[#6B5B4B]">لوحة التحكم</h2>
                        <p class="text-md text-[#9c8b7b]">نظرة عامة وشاملة على أداء الورشة.</p>
                    </div>
                    <button id="analyzePerformanceBtn" class="bg-gradient-to-r from-teal-500 to-cyan-500 hover:from-teal-600 hover:to-cyan-600 text-white font-bold py-2 px-4 rounded-lg transition flex items-center">
                        ✨ إنشاء تقرير أداء شهري
                    </button>
                </header>
                 <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                    <div class="bg-white p-6 rounded-xl shadow-md border-l-4 border-[#c7a46f]"><h3 class="text-gray-500 text-sm font-semibold">إجمالي المبيعات (تقديري)</h3><p id="kpiTotalSales" class="text-3xl font-bold text-[#6B5B4B] mt-2">0 ج.م</p></div>
                    <div class="bg-white p-6 rounded-xl shadow-md border-l-4 border-red-500"><h3 class="text-gray-500 text-sm font-semibold">إجمالي المصروفات</h3><p id="kpiTotalExpenses" class="text-3xl font-bold text-red-600 mt-2">0 ج.م</p></div>
                    <div class="bg-white p-6 rounded-xl shadow-md border-l-4 border-orange-500"><h3 class="text-gray-500 text-sm font-semibold">إجمالي رواتب العمال</h3><p id="kpiTotalWages" class="text-3xl font-bold text-orange-600 mt-2">0 ج.م</p></div>
                    <div class="bg-white p-6 rounded-xl shadow-md border-l-4 border-green-500"><h3 class="text-gray-500 text-sm font-semibold">صافي الربح (تقديري)</h3><p id="kpiNetProfit" class="text-3xl font-bold text-green-600 mt-2">0 ج.م</p></div>
                </div>
                <div class="bg-white p-6 rounded-xl shadow-md">
                    <h3 class="text-xl font-bold text-[#6B5B4B] mb-4">ملخص المصروفات الشهرية</h3>
                    <div class="chart-container">
                        <canvas id="expensesChart"></canvas>
                    </div>
                </div>
            </section>
            
            <section id="production-stages" class="page-section hidden">
                <header class="mb-8"><h2 class="text-3xl font-bold text-[#6B5B4B]">مراحل الإنتاج</h2><p class="text-md text-[#9c8b7b]">تابع كل مراحل تصنيع الشغل من الطلب إلى التسليم.</p></header>
                <div class="mb-4 border-b border-gray-200"><nav id="production-tabs" class="flex -mb-px" aria-label="Tabs"><button class="production-tab tab-active whitespace-nowrap py-4 px-4 border-b-2 font-medium text-lg" data-target="production-orders">أوامر التشغيل</button><button class="production-tab tab-inactive whitespace-nowrap py-4 px-4 border-b-2 font-medium text-lg" data-target="casting">السباكة</button><button class="production-tab tab-inactive whitespace-nowrap py-4 px-4 border-b-2 font-medium text-lg" data-target="polishing">التلميع (الجلا)</button><button class="production-tab tab-inactive whitespace-nowrap py-4 px-4 border-b-2 font-medium text-lg" data-target="filing-loss">خسية المبرد</button></nav></div>
                
                <div id="production-orders" class="production-content">
                    <div class="bg-white p-6 rounded-xl shadow-md"><div class="flex justify-between items-center mb-4"><h3 class="text-xl font-bold text-[#6B5B4B]">قائمة أوامر التشغيل</h3><div><button id="generateWorkPlanBtn" class="bg-purple-500 hover:bg-purple-600 text-white font-bold py-2 px-4 rounded-lg transition mr-4">✨ إنشاء خطة عمل يومية</button><button id="addOrderBtn" class="bg-[#c7a46f] hover:bg-[#b39362] text-white font-bold py-2 px-4 rounded-lg transition">إضافة أمر جديد</button></div></div><div class="overflow-x-auto"><table class="w-full text-sm text-left text-gray-500"><thead class="text-xs text-gray-700 uppercase bg-gray-50"><tr><th scope="col" class="px-6 py-3">التاريخ</th><th scope="col" class="px-6 py-3">العميل</th><th scope="col" class="px-6 py-3">البيان</th><th scope="col" class="px-6 py-3">الوزن</th><th scope="col" class="px-6 py-3">العامل المسؤول</th><th scope="col" class="px-6 py-3">الحالة</th></tr></thead><tbody id="ordersTableBody"></tbody></table></div></div>
                </div>

                <div id="casting" class="production-content hidden">
                    <div class="bg-white p-6 rounded-xl shadow-md"><div class="flex justify-between items-center mb-4"><h3 class="text-xl font-bold text-[#6B5B4B]">سجل السباكة</h3><button id="addCastingBtn" class="bg-[#c7a46f] hover:bg-[#b39362] text-white font-bold py-2 px-4 rounded-lg transition">إضافة عملية سباكة</button></div><div class="overflow-x-auto"><table class="w-full text-sm text-left text-gray-500"><thead class="text-xs text-gray-700 uppercase bg-gray-50"><tr><th scope="col" class="px-6 py-3">التاريخ</th><th scope="col" class="px-6 py-3">البيان</th><th scope="col" class="px-6 py-3">الوزن المرسل</th><th scope="col" class="px-6 py-3">الوزن العائد</th><th scope="col" class="px-6 py-3">الفاقد (جرام)</th><th scope="col" class="px-6 py-3">حساب الفاقد</th></tr></thead><tbody id="castingTableBody"></tbody></table></div></div>
                </div>

                <div id="polishing" class="production-content hidden">
                    <div class="bg-white p-6 rounded-xl shadow-md"><div class="flex justify-between items-center mb-4"><h3 class="text-xl font-bold text-[#6B5B4B]">سجل التلميع (الجلا)</h3><button id="addPolishingBtn" class="bg-[#c7a46f] hover:bg-[#b39362] text-white font-bold py-2 px-4 rounded-lg transition">إضافة عملية تلميع</button></div><div class="overflow-x-auto"><table class="w-full text-sm text-left text-gray-500"><thead class="text-xs text-gray-700 uppercase bg-gray-50"><tr><th scope="col" class="px-6 py-3">التاريخ</th><th scope="col" class="px-6 py-3">البيان</th><th scope="col" class="px-6 py-3">الوزن (عليه)</th><th scope="col" class="px-6 py-3">الوزن (له)</th></tr></thead><tbody id="polishingTableBody"></tbody></table></div></div>
                </div>
                
                <div id="filing-loss" class="production-content hidden">
                     <div class="bg-white p-6 rounded-xl shadow-md"><div class="flex justify-between items-center mb-4"><h3 class="text-xl font-bold text-[#6B5B4B]">سجل خسية المبرد</h3><button id="addFilingLossBtn" class="bg-[#c7a46f] hover:bg-[#b39362] text-white font-bold py-2 px-4 rounded-lg transition">إضافة خسية جديدة</button></div><div class="overflow-x-auto"><table class="w-full text-sm text-left text-gray-500"><thead class="text-xs text-gray-700 uppercase bg-gray-50"><tr><th scope="col" class="px-6 py-3">التاريخ</th><th scope="col" class="px-6 py-3">البيان</th><th scope="col" class="px-6 py-3">الوزن (جرام)</th></tr></thead><tbody id="filingLossTableBody"></tbody></table></div></div>
                </div>
            </section>
            
            <section id="inventory" class="page-section hidden">
                <header class="mb-8"><h2 class="text-3xl font-bold text-[#6B5B4B]">إدارة المخزون</h2><p class="text-md text-[#9c8b7b]">تتبع المواد الخام والمشغولات الجاهزة للبيع.</p></header>
                <div class="bg-white p-6 rounded-xl shadow-md"><div class="flex justify-between items-center mb-4"><h3 class="text-xl font-bold text-[#6B5B4B]">قائمة المواد الخام</h3><div><button id="analyzeInventoryBtn" class="bg-purple-500 hover:bg-purple-600 text-white font-bold py-2 px-4 rounded-lg transition mr-4">✨ تحليل المخزون</button><button id="addRawMaterialBtn" class="bg-[#c7a46f] hover:bg-[#b39362] text-white font-bold py-2 px-4 rounded-lg transition">إضافة مادة جديدة</button></div></div><div class="overflow-x-auto"><table class="w-full text-sm text-left text-gray-500"><thead class="text-xs text-gray-700 uppercase bg-gray-50"><tr><th scope="col" class="px-6 py-3">كود المادة</th><th scope="col" class="px-6 py-3">اسم المادة</th><th scope="col" class="px-6 py-3">الوحدة</th><th scope="col" class="px-6 py-3">الرصيد الحالي</th></tr></thead><tbody id="rawMaterialsTableBody"></tbody></table></div></div>
            </section>
            
            <section id="customers" class="page-section hidden">
                <header class="mb-8"><h2 class="text-3xl font-bold text-[#6B5B4B]">إدارة العملاء</h2><p class="text-md text-[#9c8b7b]">متابعة الأرصدة المالية وحسابات الذهب لكل عميل.</p></header>
                <div class="bg-white p-6 rounded-xl shadow-md"><div class="flex justify-between items-center mb-6 flex-wrap gap-4"><h3 class="text-xl font-bold text-[#6B5B4B]">قائمة العملاء</h3><div class="relative w-full sm:w-72"><input type="text" id="customerSearchInput" placeholder="ابحث عن عميل..." class="w-full py-2 pr-10 pl-4 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#c7a46f]"><div class="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none"><svg class="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path></svg></div></div><button id="addCustomerBtn" class="bg-[#c7a46f] hover:bg-[#b39362] text-white font-bold py-2 px-4 rounded-lg transition">إضافة عميل جديد</button></div><div class="overflow-x-auto"><table class="w-full text-sm text-left text-gray-500"><thead class="text-xs text-gray-700 uppercase bg-gray-50"><tr><th scope="col" class="px-6 py-3">اسم العميل</th><th scope="col" class="px-6 py-3">رصيد الذهب (جرام)</th><th scope="col" class="px-6 py-3">رصيد مصنعية (ج.م)</th><th scope="col" class="px-6 py-3">رصيد مصنعية ($)</th><th scope="col" class="px-6 py-3">رصيد ألماس ($)</th><th scope="col" class="px-6 py-3">رصيد ذهب مصري ($)</th><th scope="col" class="px-6 py-3">الإجراءات</th></tr></thead><tbody id="customersTableBody"></tbody></table></div></div>
            </section>
            
            <section id="customer-detail-page" class="page-section hidden">
                <header class="mb-8"><div class="flex items-center justify-between flex-wrap gap-4"><div><h2 id="customerDetailName" class="text-3xl font-bold text-[#6B5B4B]"></h2><p id="customerDetailContact" class="text-md text-[#9c8b7b]"></p></div><div class="flex items-center gap-4"><button id="generateMessageBtn" class="bg-gradient-to-r from-purple-500 to-indigo-500 hover:from-purple-600 hover:to-indigo-600 text-white font-bold py-2 px-4 rounded-lg transition flex items-center">✨ إنشاء رسالة متابعة</button><button id="backToCustomersBtn" class="bg-gray-200 hover:bg-gray-300 text-gray-800 font-bold py-2 px-4 rounded-lg transition flex items-center"><svg class="w-5 h-5 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3"></path></svg>العودة</button></div></div></header>
                <div id="customerBalanceSummary" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6 mb-8"></div>
                <div class="bg-white p-6 rounded-xl shadow-md"><div class="flex justify-between items-center mb-4"><h3 class="text-xl font-bold text-[#6B5B4B]">كشف حساب العميل</h3><button id="addOperationBtn" class="bg-[#c7a46f] hover:bg-[#b39362] text-white font-bold py-2 px-4 rounded-lg transition">إضافة عملية جديدة</button></div><div class="overflow-x-auto"><table class="w-full text-sm text-left text-gray-500"><thead class="text-xs text-gray-700 uppercase bg-gray-50"><tr><th class="px-4 py-3">التاريخ</th><th class="px-4 py-3">البيان</th><th class="px-4 py-3">مدين</th><th class="px-4 py-3">دائن</th><th class="px-4 py-3">الرصيد بعد الحركة</th><th class="px-4 py-3">نوع الرصيد</th></tr></thead><tbody id="customerTransactionsTableBody"><tr><td colspan="6" class="text-center p-4">لا توجد حركات لعرضها حالياً.</td></tr></tbody></table></div></div>
            </section>

            <section id="expenses" class="page-section hidden">
                <header class="mb-8"><h2 class="text-3xl font-bold text-[#6B5B4B]">المصروفات العامة</h2><p class="text-md text-[#9c8b7b]">سجل جميع المصروفات التشغيلية للورشة.</p></header>
                <div class="bg-white p-6 rounded-xl shadow-md"><div class="flex justify-between items-center mb-4"><h3 class="text-xl font-bold text-[#6B5B4B]">قائمة المصروفات</h3><button id="addExpenseBtn" class="bg-[#c7a46f] hover:bg-[#b39362] text-white font-bold py-2 px-4 rounded-lg transition">إضافة مصروف جديد</button></div><div class="overflow-x-auto"><table class="w-full text-sm text-left text-gray-500"><thead class="text-xs text-gray-700 uppercase bg-gray-50"><tr><th scope="col" class="px-6 py-3">التاريخ</th><th scope="col" class="px-6 py-3">البيان</th><th scope="col" class="px-6 py-3">المبلغ</th></tr></thead><tbody id="expensesTableBody"></tbody></table></div></div>
            </section>

            <section id="workers" class="page-section hidden">
                <header class="mb-8"><h2 class="text-3xl font-bold text-[#6B5B4B]">إدارة العمال</h2><p class="text-md text-[#9c8b7b]">إدارة بيانات العمال وحساباتهم المالية.</p></header>
                <div class="bg-white p-6 rounded-xl shadow-md"><div class="flex justify-between items-center mb-4"><h3 class="text-xl font-bold text-[#6B5B4B]">قائمة العمال</h3><button id="addWorkerBtn" class="bg-[#c7a46f] hover:bg-[#b39362] text-white font-bold py-2 px-4 rounded-lg transition">إضافة عامل جديد</button></div><div class="overflow-x-auto"><table class="w-full text-sm text-left text-gray-500"><thead class="text-xs text-gray-700 uppercase bg-gray-50"><tr><th scope="col" class="px-6 py-3">اسم العامل</th><th scope="col" class="px-6 py-3">الجنسية</th><th scope="col" class="px-6 py-3">رصيد ذهب (جرام)</th><th scope="col" class="px-6 py-3">رصيد نقدي (ج.م)</th><th scope="col" class="px-6 py-3">الإجراءات</th></tr></thead><tbody id="workersTableBody"></tbody></table></div></div>
            </section>

             <section id="worker-detail-page" class="page-section hidden">
                <header class="mb-8"><div class="flex items-center justify-between flex-wrap gap-4"><div><h2 id="workerDetailName" class="text-3xl font-bold text-[#6B5B4B]"></h2><p id="workerDetailNationality" class="text-md text-[#9c8b7b]"></p></div><button id="backToWorkersBtn" class="bg-gray-200 hover:bg-gray-300 text-gray-800 font-bold py-2 px-4 rounded-lg transition flex items-center"><svg class="w-5 h-5 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3"></path></svg>العودة</button></div></header>
                <div id="workerInfoSummary" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8"></div>
                <div id="workerBalanceSummary" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8"></div>
                <div class="bg-white p-6 rounded-xl shadow-md"><div class="flex justify-between items-center mb-4"><h3 class="text-xl font-bold text-[#6B5B4B]">كشف حساب العامل</h3><button id="addWorkerOperationBtn" class="bg-[#c7a46f] hover:bg-[#b39362] text-white font-bold py-2 px-4 rounded-lg transition">إضافة عملية جديدة</button></div><div class="overflow-x-auto"><table class="w-full text-sm text-left text-gray-500"><thead class="text-xs text-gray-700 uppercase bg-gray-50"><tr><th class="px-4 py-3">التاريخ</th><th class="px-4 py-3">البيان</th><th class="px-4 py-3">مدين</th><th class="px-4 py-3">دائن</th><th class="px-4 py-3">الرصيد بعد الحركة</th><th class="px-4 py-3">نوع الرصيد</th></tr></thead><tbody id="workerTransactionsTableBody"></tbody></table></div></div>
            </section>
        </main>
    </div>

    <!-- Modals -->
    <div id="rawMaterialModal" class="fixed inset-0 z-40 modal-bg flex items-center justify-center hidden"><div class="bg-white rounded-lg shadow-xl p-8 w-full max-w-md modal-content"><h3 class="text-2xl font-bold text-[#6B5B4B] mb-6">إضافة مادة خام جديدة</h3><form id="rawMaterialForm"><div class="mb-4"><label for="materialCode" class="block text-gray-700 text-sm font-bold mb-2">كود المادة</label><input type="text" id="materialCode" class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700" required></div><div class="mb-4"><label for="materialName" class="block text-gray-700 text-sm font-bold mb-2">اسم المادة</label><input type="text" id="materialName" class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700" required></div><div class="mb-4"><label for="materialUnit" class="block text-gray-700 text-sm font-bold mb-2">الوحدة</label><input type="text" id="materialUnit" value="جرام" class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700" required></div><div class="mb-6"><label for="materialStock" class="block text-gray-700 text-sm font-bold mb-2">الرصيد الافتتاحي</label><input type="number" step="0.01" id="materialStock" class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700" required></div><div class="flex items-center justify-end space-x-4" dir="ltr"><button type="submit" class="bg-[#c7a46f] hover:bg-[#b39362] text-white font-bold py-2 px-4 rounded-lg transition">حفظ</button><button id="closeRawMaterialModal" type="button" class="bg-gray-500 hover:bg-gray-600 text-white font-bold py-2 px-4 rounded-lg transition">إلغاء</button></div></form></div></div>
    <div id="customerModal" class="fixed inset-0 z-40 modal-bg flex items-center justify-center hidden"><div class="bg-white rounded-lg shadow-xl p-8 w-full max-w-lg modal-content"><h3 class="text-2xl font-bold text-[#6B5B4B] mb-6">إضافة عميل جديد</h3><form id="customerForm"><div class="mb-4"><label for="customerName" class="block text-gray-700 text-sm font-bold mb-2">اسم العميل</label><input type="text" id="customerName" class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700" required></div><div class="mb-4"><label for="customerContact" class="block text-gray-700 text-sm font-bold mb-2">بيانات الاتصال</label><input type="text" id="customerContact" class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700"></div><p class="text-sm text-gray-600 my-4">أدخل الأرصدة الافتتاحية للعميل (إن وجدت).</p><div class="grid grid-cols-2 gap-4 mb-6"><div class="mb-2"><label for="balanceGold" class="block text-sm font-bold mb-2">رصيد ذهب (جرام)</label><input type="number" step="0.01" id="balanceGold" value="0" class="shadow appearance-none border rounded w-full p-2 px-3"></div><div class="mb-2"><label for="balanceLaborEGP" class="block text-sm font-bold mb-2">رصيد مصنعية (ج.م)</label><input type="number" step="0.01" id="balanceLaborEGP" value="0" class="shadow appearance-none border rounded w-full p-2 px-3"></div><div class="mb-2"><label for="balanceLaborUSD" class="block text-sm font-bold mb-2">رصيد مصنعية ($)</label><input type="number" step="0.01" id="balanceLaborUSD" value="0" class="shadow appearance-none border rounded w-full p-2 px-3"></div><div class="mb-2"><label for="balanceEgyptianGoldUSD" class="block text-sm font-bold mb-2">رصيد ذهب مصري ($)</label><input type="number" step="0.01" id="balanceEgyptianGoldUSD" value="0" class="shadow appearance-none border rounded w-full p-2 px-3"></div></div><fieldset class="border border-gray-300 p-4 rounded-lg"><legend class="px-2 font-semibold text-gray-700">تفاصيل رصيد الألماس</legend><div class="grid grid-cols-2 lg:grid-cols-4 gap-4"><div class="lg:col-span-2"><label for="balanceDiamondValueUSD" class="block text-sm font-bold mb-1">قيمة رصيد ألماس ($)</label><input type="number" step="0.01" id="balanceDiamondValueUSD" value="0" class="shadow w-full p-2 border rounded"></div><div><label for="balanceDiamondWeight" class="block text-sm font-bold mb-1">الوزن (قيراط)</label><input type="number" step="0.01" id="balanceDiamondWeight" value="0" class="shadow w-full p-2 border rounded"></div><div><label for="balanceDiamondCount" class="block text-sm font-bold mb-1">العدد</label><input type="number" id="balanceDiamondCount" value="0" class="shadow w-full p-2 border rounded"></div></div><div class="mt-4"><label for="balanceDiamondSizes" class="block text-sm font-bold mb-1">المقاسات (وصف)</label><input type="text" id="balanceDiamondSizes" placeholder="مثال: 0.5mm, 1mm" class="shadow w-full p-2 border rounded"></div></fieldset><div class="flex items-center justify-end space-x-4 mt-6" dir="ltr"><button type="submit" class="bg-[#c7a46f] hover:bg-[#b39362] text-white font-bold py-2 px-4 rounded-lg transition">حفظ</button><button id="closeCustomerModal" type="button" class="bg-gray-500 hover:bg-gray-600 text-white font-bold py-2 px-4 rounded-lg transition">إلغاء</button></div></form></div></div>
    <div id="operationModal" class="fixed inset-0 z-40 modal-bg flex items-center justify-center hidden"><div class="bg-white rounded-lg shadow-xl p-8 w-full max-w-2xl modal-content"><h3 class="text-2xl font-bold text-[#6B5B4B] mb-6">إضافة عملية جديدة</h3><form id="operationForm"><div class="grid grid-cols-1 md:grid-cols-2 gap-6"><div class="md:col-span-2"><label for="operationType" class="block text-gray-700 text-sm font-bold mb-2">نوع العملية</label><select id="operationType" class="shadow border rounded w-full py-2 px-3 text-gray-700"><option value="">-- اختر نوع العملية --</option><option value="delivery">تسليم شغل للعميل</option><option value="payment">تسوية حساب (دفعة)</option></select></div></div><div id="operationFieldsContainer" class="mt-6 space-y-4"></div><div class="flex items-center justify-end space-x-4 mt-8" dir="ltr"><button type="submit" class="bg-[#c7a46f] hover:bg-[#b39362] text-white font-bold py-2 px-4 rounded-lg transition">حفظ</button><button id="closeOperationModal" type="button" class="bg-gray-500 hover:bg-gray-600 text-white font-bold py-2 px-4 rounded-lg transition">إلغاء</button></div></form></div></div>
    <div id="geminiModal" class="fixed inset-0 z-50 modal-bg flex items-center justify-center hidden"><div class="bg-white rounded-lg shadow-xl p-8 w-full max-w-lg modal-content"><h3 class="text-2xl font-bold text-[#6B5B4B] mb-4">✨ رسالة المتابعة المقترحة</h3><div id="geminiResultContainer" class="w-full h-48 bg-gray-100 rounded-lg p-4 mb-4 overflow-y-auto"><div class="flex justify-center items-center h-full"><div class="loader"></div></div></div><div class="flex items-center justify-end space-x-4" dir="ltr"><button id="copyGeminiMessageBtn" class="bg-blue-500 hover:bg-blue-600 text-white font-bold py-2 px-4 rounded-lg transition">نسخ</button><button id="closeGeminiModal" type="button" class="bg-gray-500 hover:bg-gray-600 text-white font-bold py-2 px-4 rounded-lg transition">إلغاء</button></div></div></div>
    <div id="expenseModal" class="fixed inset-0 z-50 modal-bg flex items-center justify-center hidden"><div class="bg-white rounded-lg shadow-xl p-8 w-full max-w-lg modal-content"><h3 class="text-2xl font-bold text-[#6B5B4B] mb-6">إضافة مصروف جديد</h3><form id="expenseForm"><div class="mb-4"><label for="expenseDate" class="block text-gray-700 text-sm font-bold mb-2">التاريخ</label><input type="date" id="expenseDate" class="shadow w-full p-2 border rounded" required></div><div class="mb-4 relative"><label for="expenseDesc" class="block text-gray-700 text-sm font-bold mb-2">البيان</label><input type="text" id="expenseDesc" class="shadow w-full p-2 border rounded" required><button type="button" id="suggestDescBtn" class="absolute left-2 top-9 text-xs bg-purple-100 text-purple-700 px-2 py-1 rounded">✨ اقتراح</button></div><div class="grid grid-cols-2 lg:grid-cols-3 gap-4"><div class="lg:col-span-1"><label for="expenseAmount" class="block text-gray-700 text-sm font-bold mb-2">المبلغ</label><input type="number" step="0.01" id="expenseAmount" class="shadow w-full p-2 border rounded" required></div><div><label for="expenseCurrency" class="block text-gray-700 text-sm font-bold mb-2">العملة</label><select id="expenseCurrency" class="shadow w-full p-2 border rounded"><option value="EGP">جنيه مصري (EGP)</option><option value="USD">دولار أمريكي (USD)</option></select></div><div id="expenseExchangeRateWrapper" class="hidden"><label class="block text-sm font-bold mb-2">سعر الصرف</label><input type="number" step="0.01" id="expenseExchangeRate" class="shadow w-full p-2 border rounded"></div></div><div class="flex items-center justify-end space-x-4 mt-6" dir="ltr"><button type="submit" class="bg-[#c7a46f] hover:bg-[#b39362] text-white font-bold py-2 px-4 rounded-lg transition">حفظ</button><button id="closeExpenseModal" type="button" class="bg-gray-500 hover:bg-gray-600 text-white font-bold py-2 px-4 rounded-lg transition">إلغاء</button></div></form></div></div>
    <div id="workerModal" class="fixed inset-0 z-40 modal-bg flex items-center justify-center hidden"><div class="bg-white rounded-lg shadow-xl p-8 w-full max-w-lg modal-content"><h3 class="text-2xl font-bold text-[#6B5B4B] mb-6">إضافة عامل جديد</h3><form id="workerForm"><div class="grid grid-cols-2 gap-4"><div class="mb-4"><label for="workerName" class="block text-gray-700 text-sm font-bold mb-2">اسم العامل</label><input type="text" id="workerName" class="shadow w-full p-2 border rounded" required></div><div class="mb-4"><label for="workerNationality" class="block text-gray-700 text-sm font-bold mb-2">الجنسية</label><input type="text" id="workerNationality" class="shadow w-full p-2 border rounded" required></div><div class="mb-4"><label for="workerStartDate" class="block text-gray-700 text-sm font-bold mb-2">تاريخ بدء العمل</label><input type="date" id="workerStartDate" class="shadow w-full p-2 border rounded" required></div><div class="mb-4"><label for="workerSalaryEGP" class="block text-gray-700 text-sm font-bold mb-2">الراتب (ج.م)</label><input type="number" step="0.01" id="workerSalaryEGP" class="shadow w-full p-2 border rounded" placeholder="0"></div><div class="mb-4 col-span-2"><label for="workerSalaryUSD" class="block text-gray-700 text-sm font-bold mb-2">الراتب ($)</label><input type="number" step="0.01" id="workerSalaryUSD" class="shadow w-full p-2 border rounded" placeholder="0"></div><div class="mb-4 col-span-2"><label for="workerNotes" class="block text-gray-700 text-sm font-bold mb-2">ملاحظات (بيان)</label><textarea id="workerNotes" class="shadow w-full p-2 border rounded" rows="3"></textarea></div></div><div class="flex items-center justify-end space-x-4 mt-6" dir="ltr"><button type="submit" class="bg-[#c7a46f] hover:bg-[#b39362] text-white font-bold py-2 px-4 rounded-lg transition">حفظ</button><button id="closeWorkerModal" type="button" class="bg-gray-500 hover:bg-gray-600 text-white font-bold py-2 px-4 rounded-lg transition">إلغاء</button></div></form></div></div>
    <div id="workerOperationModal" class="fixed inset-0 z-40 modal-bg flex items-center justify-center hidden"><div class="bg-white rounded-lg shadow-xl p-8 w-full max-w-2xl modal-content"><h3 class="text-2xl font-bold text-[#6B5B4B] mb-6">إضافة عملية للعامل</h3><form id="workerOperationForm"><div class="md:col-span-2 mb-4"><label for="workerOperationType" class="block text-gray-700 text-sm font-bold mb-2">نوع العملية</label><select id="workerOperationType" class="shadow border rounded w-full py-2 px-3 text-gray-700"><option value="">-- اختر نوع العملية --</option><option value="work_assignment">تسليم شغل (عهدة)</option><option value="payment">تسوية حساب (دفعة/راتب)</option></select></div><div id="workerOperationFieldsContainer" class="mt-6 space-y-4"></div><div class="flex items-center justify-end space-x-4 mt-8" dir="ltr"><button type="submit" class="bg-[#c7a46f] hover:bg-[#b39362] text-white font-bold py-2 px-4 rounded-lg transition">حفظ</button><button id="closeWorkerOperationModal" type="button" class="bg-gray-500 hover:bg-gray-600 text-white font-bold py-2 px-4 rounded-lg transition">إلغاء</button></div></form></div></div>
    <div id="dashboardAnalysisModal" class="fixed inset-0 z-50 modal-bg flex items-center justify-center hidden"><div class="bg-white rounded-lg shadow-xl p-8 w-full max-w-2xl modal-content"><h3 class="text-2xl font-bold text-[#6B5B4B] mb-4">✨ تحليل الأداء الشهري</h3><div id="analysisResultContainer" class="w-full min-h-[200px] bg-gray-100 rounded-lg p-4 mb-4 overflow-y-auto"><div class="flex justify-center items-center h-full"><div class="loader"></div></div></div><div class="flex items-center justify-end" dir="ltr"><button id="closeAnalysisModal" type="button" class="bg-gray-500 hover:bg-gray-600 text-white font-bold py-2 px-4 rounded-lg transition">إغلاق</button></div></div></div>
    <div id="orderModal" class="fixed inset-0 z-40 modal-bg flex items-center justify-center hidden"><div class="bg-white rounded-lg shadow-xl p-8 w-full max-w-2xl modal-content"><h3 class="text-2xl font-bold text-[#6B5B4B] mb-6">إضافة أمر تشغيل جديد</h3><form id="orderForm" class="space-y-4"><div class="grid grid-cols-1 md:grid-cols-2 gap-4"><div><label for="orderCustomer" class="block text-sm font-bold mb-2">العميل</label><select id="orderCustomer" class="shadow w-full p-2 border rounded" required></select></div><div><label for="orderDate" class="block text-sm font-bold mb-2">تاريخ الطلب</label><input type="date" id="orderDate" class="shadow w-full p-2 border rounded" required></div><div><label for="orderItem" class="block text-sm font-bold mb-2">الصنف</label><input type="text" id="orderItem" placeholder="خاتم، سلسلة،..." class="shadow w-full p-2 border rounded"></div><div><label for="orderDescription" class="block text-sm font-bold mb-2">البيان (الوصف)</label><input type="text" id="orderDescription" class="shadow w-full p-2 border rounded" required></div><div><label for="orderCarat" class="block text-sm font-bold mb-2">العيار</label><input type="number" id="orderCarat" value="21" class="shadow w-full p-2 border rounded"></div><div><label for="orderWeight" class="block text-sm font-bold mb-2">الوزن (جرام)</label><input type="number" step="0.01" id="orderWeight" class="shadow w-full p-2 border rounded"></div><div><label for="orderQuantity" class="block text-sm font-bold mb-2">العدد</label><input type="number" id="orderQuantity" value="1" class="shadow w-full p-2 border rounded"></div><div><label for="orderWorker" class="block text-sm font-bold mb-2">العامل المسؤول</label><select id="orderWorker" class="shadow w-full p-2 border rounded"><option value="">-- لم يعين --</option></select></div></div><div><label for="orderNotes" class="block text-sm font-bold mb-2">ملاحظات</label><textarea id="orderNotes" rows="2" class="shadow w-full p-2 border rounded"></textarea></div><div class="flex items-center justify-end space-x-4 mt-6" dir="ltr"><button type="submit" class="bg-[#c7a46f] hover:bg-[#b39362] text-white font-bold py-2 px-4 rounded-lg transition">حفظ الأمر</button><button id="closeOrderModal" type="button" class="bg-gray-500 hover:bg-gray-600 text-white font-bold py-2 px-4 rounded-lg transition">إلغاء</button></div></form></div></div>
    <div id="castingModal" class="fixed inset-0 z-50 modal-bg flex items-center justify-center hidden"><div class="bg-white rounded-lg shadow-xl p-8 w-full max-w-2xl modal-content"><h3 class="text-2xl font-bold text-[#6B5B4B] mb-6">إضافة عملية سباكة</h3><form id="castingForm" class="space-y-4"><div><label class="block text-sm font-bold mb-2">التاريخ</label><input type="date" id="castingDate" class="shadow w-full p-2 border rounded" required></div><div><label class="block text-sm font-bold mb-2">البيان (الوصف)</label><input type="text" id="castingDescription" class="shadow w-full p-2 border rounded" required></div><div class="grid grid-cols-2 gap-4"><div><label class="block text-sm font-bold mb-2">الوزن المرسل (جرام)</label><input type="number" step="0.01" id="castingWeightSent" class="shadow w-full p-2 border rounded" required></div><div><label class="block text-sm font-bold mb-2">الوزن العائد (جرام)</label><input type="number" step="0.01" id="castingWeightReturned" class="shadow w-full p-2 border rounded" required></div></div><div><label class="block text-sm font-bold mb-2">حساب الفاقد</label><input type="text" id="castingLossCalculation" class="shadow w-full p-2 border rounded" placeholder="مثال: 5 جرام لكل كيلو"></div><div class="flex items-center justify-end space-x-4 mt-6" dir="ltr"><button type="submit" class="bg-[#c7a46f] hover:bg-[#b39362] text-white font-bold py-2 px-4 rounded-lg transition">حفظ العملية</button><button id="closeCastingModal" type="button" class="bg-gray-500 hover:bg-gray-600 text-white font-bold py-2 px-4 rounded-lg transition">إلغاء</button></div></form></div></div>
    <div id="polishingModal" class="fixed inset-0 z-50 modal-bg flex items-center justify-center hidden"><div class="bg-white rounded-lg shadow-xl p-8 w-full max-w-2xl modal-content"><h3 class="text-2xl font-bold text-[#6B5B4B] mb-6">إضافة عملية تلميع (جلا)</h3><form id="polishingForm" class="space-y-4"><div><label class="block text-sm font-bold mb-2">التاريخ</label><input type="date" id="polishingDate" class="shadow w-full p-2 border rounded" required></div><div><label class="block text-sm font-bold mb-2">البيان (الوصف)</label><input type="text" id="polishingDescription" class="shadow w-full p-2 border rounded" required></div><div class="grid grid-cols-2 gap-4"><div><label class="block text-sm font-bold mb-2">الوزن المسلم (عليه)</label><input type="number" step="0.01" id="polishingWeightDebit" class="shadow w-full p-2 border rounded" placeholder="0"></div><div><label class="block text-sm font-bold mb-2">الوزن العائد (له)</label><input type="number" step="0.01" id="polishingWeightCredit" class="shadow w-full p-2 border rounded" placeholder="0"></div></div><div class="flex items-center justify-end space-x-4 mt-6" dir="ltr"><button type="submit" class="bg-[#c7a46f] hover:bg-[#b39362] text-white font-bold py-2 px-4 rounded-lg transition">حفظ العملية</button><button id="closePolishingModal" type="button" class="bg-gray-500 hover:bg-gray-600 text-white font-bold py-2 px-4 rounded-lg transition">إلغاء</button></div></form></div></div>
    <div id="filingLossModal" class="fixed inset-0 z-50 modal-bg flex items-center justify-center hidden"><div class="bg-white rounded-lg shadow-xl p-8 w-full max-w-lg modal-content"><h3 class="text-2xl font-bold text-[#6B5B4B] mb-6">إضافة خسية مبرد</h3><form id="filingLossForm" class="space-y-4"><div><label class="block text-sm font-bold mb-2">التاريخ</label><input type="date" id="filingLossDate" class="shadow w-full p-2 border rounded" required></div><div><label class="block text-sm font-bold mb-2">البيان (الوصف)</label><input type="text" id="filingLossDescription" class="shadow w-full p-2 border rounded" required></div><div><label class="block text-sm font-bold mb-2">الوزن (جرام)</label><input type="number" step="0.01" id="filingLossWeight" class="shadow w-full p-2 border rounded" required></div><div class="flex items-center justify-end space-x-4 mt-6" dir="ltr"><button type="submit" class="bg-[#c7a46f] hover:bg-[#b39362] text-white font-bold py-2 px-4 rounded-lg transition">حفظ</button><button id="closeFilingLossModal" type="button" class="bg-gray-500 hover:bg-gray-600 text-white font-bold py-2 px-4 rounded-lg transition">إلغاء</button></div></form></div></div>

    <script type="module">
        import { initializeApp } from "https://www.gstatic.com/firebasejs/11.6.1/firebase-app.js";
        import { getAuth, signInAnonymously, onAuthStateChanged, signInWithCustomToken } from "https://www.gstatic.com/firebasejs/11.6.1/firebase-auth.js";
        import { getFirestore, collection, onSnapshot, addDoc, doc, getDoc, setDoc, writeBatch, runTransaction, query, orderBy, getDocs, setLogLevel } from "https://www.gstatic.com/firebasejs/11.6.1/firebase-firestore.js";
        
        const firebaseConfig = typeof __firebase_config !== 'undefined' ? JSON.parse(__firebase_config) : {};
        
        setLogLevel('debug');
        const app = initializeApp(firebaseConfig);
        const auth = getAuth(app);
        const db = getFirestore(app);
        const loadingOverlay = document.getElementById('loading-overlay');
        let currentUserId = null;
        let allCustomersData = [];
        let allWorkersData = [];
        let currentOpenCustomerId = null;
        let currentOpenWorkerId = null;
        let unsubscribeTransactions = null;
        let unsubscribeWorkerTransactions = null;
        let expensesChart = null;
        
        const appId = typeof __app_id !== 'undefined' ? __app_id : 'default-app-id';

        onAuthStateChanged(auth, user => {
            if (user) {
                currentUserId = user.uid;
                document.getElementById('userId').textContent = currentUserId;
                loadAllData();
            } else {
                authenticate();
            }
        });

        async function authenticate() {
             try {
                if (typeof __initial_auth_token !== 'undefined' && __initial_auth_token) {
                    await signInWithCustomToken(auth, __initial_auth_token);
                } else {
                    await signInAnonymously(auth);
                }
            } catch (error) {
                 console.error("Authentication failed:", error);
                 loadingOverlay.innerHTML = `<p class="text-center text-red-500 p-4">فشل المصادقة.<br>${error.message}</p>`;
            }
        }

        function loadAllData() {
            loadRawMaterials();
            loadCustomers();
            loadDashboardData();
            loadExpenses();
            loadWorkers();
            loadProductionOrders();
            loadCastingJobs();
            loadPolishingJobs();
            loadFilingLoss();
            loadingOverlay.classList.add('hidden');
        }

        function formatCurrency(value, currency = 'EGP') { return new Intl.NumberFormat('ar-EG', { style: 'currency', currency: currency, minimumFractionDigits: 2 }).format(value || 0); }
        function formatNumber(value) { return new Intl.NumberFormat('ar-EG', { minimumFractionDigits: 2, maximumFractionDigits: 2 }).format(value || 0); }
        function getBalanceColor(value) { return value >= 0 ? 'text-green-600' : 'text-red-600'; }
        
        async function loadDashboardData() {
            if(!currentUserId) return;
            const expensesQuery = query(collection(db, 'artifacts', appId, 'users', currentUserId, 'general_expenses'));
            const workersQuery = query(collection(db, 'artifacts', appId, 'users', currentUserId, 'workers'));
            
            const [expensesSnap, workersSnap] = await Promise.all([getDocs(expensesQuery), getDocs(workersQuery)]);
            
            const totalExpenses = expensesSnap.docs.reduce((sum, doc) => sum + doc.data().amount, 0);
            const totalWages = workersSnap.docs.reduce((sum, doc) => sum + (doc.data().salaryEGP || 0), 0);
            
            document.getElementById('kpiTotalExpenses').textContent = formatCurrency(totalExpenses);
            document.getElementById('kpiTotalWages').textContent = formatCurrency(totalWages);

            if (expensesChart) expensesChart.destroy();
            const ctx = document.getElementById('expensesChart').getContext('2d');
            expensesChart = new Chart(ctx, {
                type: 'bar',
                data: {
                    labels: ['المصروفات العامة', 'رواتب العمال'],
                    datasets: [{
                        label: 'التكاليف الشهرية (بالجنيه المصري)',
                        data: [totalExpenses, totalWages],
                        backgroundColor: ['rgba(239, 68, 68, 0.6)', 'rgba(249, 115, 22, 0.6)'],
                        borderColor: ['rgba(239, 68, 68, 1)', 'rgba(249, 115, 22, 1)'],
                        borderWidth: 1,
                        borderRadius: 5,
                    }]
                },
                options: { responsive: true, maintainAspectRatio: false, scales: { y: { beginAtZero: true } } }
            });
        }
        
        function loadRawMaterials() {
            if (!currentUserId) return;
            const rawMaterialsCol = collection(db, 'artifacts', appId, 'users', currentUserId, 'raw_materials');
            onSnapshot(rawMaterialsCol, (snapshot) => {
                const tableBody = document.getElementById('rawMaterialsTableBody');
                tableBody.innerHTML = '';
                if (snapshot.empty) { tableBody.innerHTML = '<tr><td colspan="4" class="text-center p-4">لم يتم إضافة أي مواد خام بعد.</td></tr>'; return; }
                snapshot.docs.forEach(doc => {
                    const material = doc.data();
                    const row = `<tr class="bg-white border-b hover:bg-gray-50"><td class="px-6 py-4">${material.code}</td><td class="px-6 py-4">${material.name}</td><td class="px-6 py-4">${material.unit}</td><td class="px-6 py-4 font-bold">${material.stock}</td></tr>`;
                    tableBody.innerHTML += row;
                });
            }, error => { console.error("Error loading raw materials: ", error); tableBody.innerHTML = '<tr><td colspan="4" class="text-center p-4 text-red-500">حدث خطأ.</td></tr>'; });
        }
        document.getElementById('addRawMaterialBtn').addEventListener('click', () => document.getElementById('rawMaterialModal').classList.remove('hidden'));
        document.getElementById('closeRawMaterialModal').addEventListener('click', () => document.getElementById('rawMaterialModal').classList.add('hidden'));
        document.getElementById('rawMaterialForm').addEventListener('submit', async (e) => {
            e.preventDefault(); if (!currentUserId) return;
            const newMaterial = { code: document.getElementById('materialCode').value, name: document.getElementById('materialName').value, unit: document.getElementById('materialUnit').value, stock: parseFloat(document.getElementById('materialStock').value), createdAt: new Date() };
            try { await addDoc(collection(db, 'artifacts', appId, 'users', currentUserId, 'raw_materials'), newMaterial); document.getElementById('rawMaterialForm').reset(); document.getElementById('rawMaterialModal').classList.add('hidden'); } catch (error) { console.error("Error adding raw material: ", error); }
        });

        function loadCustomers() {
            if (!currentUserId) return;
            const q = query(collection(db, 'artifacts', appId, 'users', currentUserId, 'customers'), orderBy("name"));
            onSnapshot(q, (snapshot) => {
                allCustomersData = snapshot.docs;
                renderCustomersTable(allCustomersData);
                const customerSelect = document.getElementById('orderCustomer');
                customerSelect.innerHTML = '<option value="">-- اختر العميل --</option>';
                allCustomersData.forEach(doc => {
                    customerSelect.innerHTML += `<option value="${doc.id}">${doc.data().name}</option>`;
                });
                if (currentOpenCustomerId) {
                    const updatedDoc = allCustomersData.find(doc => doc.id === currentOpenCustomerId);
                    if (updatedDoc) renderCustomerDetailPage(updatedDoc.data());
                }
            }, error => { console.error("Error loading customers: ", error); document.getElementById('customersTableBody').innerHTML = '<tr><td colspan="7" class="text-center p-4 text-red-500">حدث خطأ.</td></tr>'; });
        }
        function renderCustomersTable(customers) {
            const tableBody = document.getElementById('customersTableBody');
            tableBody.innerHTML = '';
             if (customers.length === 0) { tableBody.innerHTML = '<tr><td colspan="7" class="text-center p-4">لا توجد نتائج.</td></tr>'; return; }
            customers.forEach(doc => {
                const customer = doc.data();
                const row = `<tr class="bg-white border-b hover:bg-gray-50"><td class="px-6 py-4 font-medium text-gray-900">${customer.name}</td><td class="px-6 py-4 font-bold ${getBalanceColor(customer.balanceGold)}">${formatNumber(customer.balanceGold)}</td><td class="px-6 py-4 font-bold ${getBalanceColor(customer.balanceLaborEGP)}">${formatCurrency(customer.balanceLaborEGP)}</td><td class="px-6 py-4 font-bold ${getBalanceColor(customer.balanceLaborUSD)}">${formatCurrency(customer.balanceLaborUSD, 'USD')}</td><td class="px-6 py-4 font-bold ${getBalanceColor(customer.balanceDiamondUSD)}">${formatCurrency(customer.balanceDiamondUSD, 'USD')}</td><td class="px-6 py-4 font-bold ${getBalanceColor(customer.balanceEgyptianGoldUSD)}">${formatCurrency(customer.balanceEgyptianGoldUSD, 'USD')}</td><td class="px-6 py-4"><button data-id="${doc.id}" class="view-details-btn text-blue-600 hover:underline">عرض التفاصيل</button></td></tr>`;
                tableBody.innerHTML += row;
            });
            document.querySelectorAll('.view-details-btn').forEach(button => button.addEventListener('click', (e) => navigateToCustomerDetail(e.target.dataset.id)));
        }
        document.getElementById('customerSearchInput').addEventListener('input', (e) => renderCustomersTable(allCustomersData.filter(doc => doc.data().name.toLowerCase().includes(e.target.value.toLowerCase().trim()))));
        function navigateToCustomerDetail(customerId) {
            const customerDoc = allCustomersData.find(doc => doc.id === customerId);
            if (!customerDoc) return;
            currentOpenCustomerId = customerId;
            renderCustomerDetailPage(customerDoc.data());
            loadTransactions(customerId);
            document.getElementById('customers').classList.add('hidden');
            document.getElementById('customer-detail-page').classList.remove('hidden');
        }
        function renderCustomerDetailPage(customer) {
            document.getElementById('customerDetailName').textContent = customer.name;
            document.getElementById('customerDetailContact').textContent = customer.contact || 'لا توجد بيانات اتصال';
            const summaryContainer = document.getElementById('customerBalanceSummary');
            summaryContainer.innerHTML = `<div class="bg-white p-4 rounded-xl shadow-md border-l-4 ${getBalanceColor(customer.balanceGold)}"><h4 class="text-gray-500 text-sm">رصيد ذهب</h4><p class="text-2xl font-bold">${formatNumber(customer.balanceGold)} جرام</p></div><div class="bg-white p-4 rounded-xl shadow-md border-l-4 ${getBalanceColor(customer.balanceLaborEGP)}"><h4 class="text-gray-500 text-sm">مصنعية (ج.م)</h4><p class="text-2xl font-bold">${formatCurrency(customer.balanceLaborEGP)}</p></div><div class="bg-white p-4 rounded-xl shadow-md border-l-4 ${getBalanceColor(customer.balanceLaborUSD)}"><h4 class="text-gray-500 text-sm">مصنعية ($)</h4><p class="text-2xl font-bold">${formatCurrency(customer.balanceLaborUSD, 'USD')}</p></div><div class="bg-white p-4 rounded-xl shadow-md border-l-4 ${getBalanceColor(customer.balanceDiamondUSD)}"><h4 class="text-gray-500 text-sm">ألماس ($)</h4><p class="text-2xl font-bold">${formatCurrency(customer.balanceDiamondUSD, 'USD')}</p></div><div class="bg-white p-4 rounded-xl shadow-md border-l-4 ${getBalanceColor(customer.balanceEgyptianGoldUSD)}"><h4 class="text-gray-500 text-sm">ذهب مصري ($)</h4><p class="text-2xl font-bold">${formatCurrency(customer.balanceEgyptianGoldUSD, 'USD')}</p></div>`;
        }
        function loadTransactions(customerId) {
            if (unsubscribeTransactions) unsubscribeTransactions();
            const q = query(collection(db, 'artifacts', appId, 'users', currentUserId, 'customers', customerId, 'transactions'), orderBy("createdAt", "desc"));
            unsubscribeTransactions = onSnapshot(q, (snapshot) => {
                const tableBody = document.getElementById('customerTransactionsTableBody');
                tableBody.innerHTML = '';
                if(snapshot.empty) { tableBody.innerHTML = '<tr><td colspan="6" class="text-center p-4">لا توجد حركات.</td></tr>'; return; }
                
                let runningBalances = {};
                const customerDoc = allCustomersData.find(doc => doc.id === customerId);
                if (customerDoc) runningBalances = { ...customerDoc.data() };

                const transactions = snapshot.docs.map(doc => doc.data());
                for (const trans of transactions) {
                    const isDebit = trans.type === 'debit';
                    const isGold = trans.balanceType === 'balanceGold';
                    const currency = isGold ? null : trans.balanceType.slice(-3).toUpperCase();
                    const amountFormatted = isGold ? `${formatNumber(trans.amount)} جرام` : formatCurrency(trans.amount, currency);
                    const balanceTypeArabic = { balanceGold: 'ذهب', balanceLaborEGP: 'مصنعية (ج.م)', balanceLaborUSD: 'مصنعية ($)', balanceDiamondUSD: 'ألماس ($)', balanceEgyptianGoldUSD: 'ذهب مصري ($)'}[trans.balanceType];
                    const runningBalanceFormatted = isGold ? `${formatNumber(runningBalances[trans.balanceType])} جرام` : formatCurrency(runningBalances[trans.balanceType], currency);
                    const row = `<tr class="bg-white border-b"><td class="px-4 py-3">${new Date(trans.createdAt.seconds * 1000).toLocaleDateString('ar-EG')}</td><td class="px-4 py-3">${trans.description}</td><td class="px-4 py-3 text-red-600 font-semibold">${isDebit ? amountFormatted : '-'}</td><td class="px-4 py-3 text-green-600 font-semibold">${!isDebit ? amountFormatted : '-'}</td><td class="px-4 py-3 font-bold ${getBalanceColor(runningBalances[trans.balanceType])}">${runningBalanceFormatted}</td><td class="px-4 py-3">${balanceTypeArabic}</td></tr>`;
                    tableBody.innerHTML += row;
                    if (isDebit) runningBalances[trans.balanceType] -= trans.amount; else runningBalances[trans.balanceType] += trans.amount;
                }
            });
        }
        document.getElementById('backToCustomersBtn').addEventListener('click', () => {
            if (unsubscribeTransactions) unsubscribeTransactions();
            currentOpenCustomerId = null;
            document.getElementById('customer-detail-page').classList.add('hidden');
            document.getElementById('customers').classList.remove('hidden');
        });
        document.getElementById('addCustomerBtn').addEventListener('click', () => document.getElementById('customerModal').classList.remove('hidden'));
        document.getElementById('closeCustomerModal').addEventListener('click', () => document.getElementById('customerModal').classList.add('hidden'));
        document.getElementById('customerForm').addEventListener('submit', async (e) => {
            e.preventDefault(); if (!currentUserId) return;
            const newCustomer = { name: document.getElementById('customerName').value, contact: document.getElementById('customerContact').value, balanceGold: parseFloat(document.getElementById('balanceGold').value) || 0, balanceLaborEGP: parseFloat(document.getElementById('balanceLaborEGP').value) || 0, balanceLaborUSD: parseFloat(document.getElementById('balanceLaborUSD').value) || 0, balanceEgyptianGoldUSD: parseFloat(document.getElementById('balanceEgyptianGoldUSD').value) || 0, balanceDiamondUSD: parseFloat(document.getElementById('balanceDiamondValueUSD').value) || 0, balanceDiamondWeight: parseFloat(document.getElementById('balanceDiamondWeight').value) || 0, balanceDiamondCount: parseInt(document.getElementById('balanceDiamondCount').value) || 0, balanceDiamondSizes: document.getElementById('balanceDiamondSizes').value || '', createdAt: new Date() };
            try {
                const customerRef = await addDoc(collection(db, 'artifacts', appId, 'users', currentUserId, 'customers'), newCustomer);
                const batch = writeBatch(db);
                const openingBalances = { balanceGold: newCustomer.balanceGold, balanceLaborEGP: newCustomer.balanceLaborEGP, balanceLaborUSD: newCustomer.balanceLaborUSD, balanceDiamondUSD: newCustomer.balanceDiamondUSD, balanceEgyptianGoldUSD: newCustomer.balanceEgyptianGoldUSD };
                for (const [balanceType, amount] of Object.entries(openingBalances)) {
                    if (amount !== 0) batch.set(doc(collection(customerRef, 'transactions')), { description: "رصيد افتتاحي", type: "debit", balanceType, amount, createdAt: newCustomer.createdAt });
                }
                await batch.commit();
                document.getElementById('customerForm').reset();
                document.getElementById('customerModal').classList.add('hidden');
            } catch (error) { console.error("Error adding customer: ", error); }
        });
        
        // Customer Operation Modal Logic
        const operationTypeSelect = document.getElementById('operationType');
        const operationFieldsContainer = document.getElementById('operationFieldsContainer');
        document.getElementById('addOperationBtn').addEventListener('click', () => {
            document.getElementById('operationModal').classList.remove('hidden');
            document.getElementById('operationForm').reset();
            operationFieldsContainer.innerHTML = '';
        });
        document.getElementById('closeOperationModal').addEventListener('click', () => document.getElementById('operationModal').classList.add('hidden'));
        operationFieldsContainer.addEventListener('change', (e) => {
            if (e.target.id === 'opPaymentType') {
                const balanceType = e.target.value;
                const paymentCurrencyWrapper = document.getElementById('paymentCurrencyWrapper');
                const exchangeRateWrapper = document.getElementById('exchangeRateWrapper');
                if (balanceType.includes('USD')) paymentCurrencyWrapper.classList.remove('hidden'); else { paymentCurrencyWrapper.classList.add('hidden'); exchangeRateWrapper.classList.add('hidden'); }
                updatePaymentDescription();
            }
            if (e.target.id === 'opPaymentCurrency') {
                document.getElementById('exchangeRateWrapper').classList.toggle('hidden', e.target.value !== 'EGP');
                updatePaymentDescription();
            }
        });
        function updatePaymentDescription() {
            const opDescInput = document.getElementById('opDesc');
            if (!opDescInput) return;
            const paymentTypeSelect = document.getElementById('opPaymentType');
            const selectedText = paymentTypeSelect.options[paymentTypeSelect.selectedIndex].text;
            opDescInput.value = `تسوية ${selectedText}`;
        }
        operationTypeSelect.addEventListener('change', (e) => {
            const type = e.target.value;
            operationFieldsContainer.innerHTML = '';
            if (type === 'delivery') {
                operationFieldsContainer.innerHTML = `<div class="mb-4 relative"><label class="block text-gray-700 text-sm font-bold mb-2">وصف العملية</label><input type="text" id="opDesc" class="shadow w-full p-2 border rounded" required placeholder="مثال: تسليم طقم كامل"><button type="button" id="suggestCustomerOpDescBtn" class="absolute left-2 top-9 text-xs bg-purple-100 text-purple-700 px-2 py-1 rounded">✨ اقتراح</button></div><div class="grid grid-cols-2 gap-4"><div><label class="block text-sm mb-1">ذهب (جرام)</label><input type="number" step="0.01" id="opGold" class="shadow w-full p-2 border rounded" placeholder="0"></div><div><label class="block text-sm mb-1">مصنعية (ج.م)</label><input type="number" step="0.01" id="opLaborEGP" class="shadow w-full p-2 border rounded" placeholder="0"></div><div><label class="block text-sm mb-1">مصنعية ($)</label><input type="number" step="0.01" id="opLaborUSD" class="shadow w-full p-2 border rounded" placeholder="0"></div><div><label class="block text-sm mb-1">ألماس ($)</label><input type="number" step="0.01" id="opDiamondUSD" class="shadow w-full p-2 border rounded" placeholder="0"></div></div>`;
            } else if (type === 'payment') {
                operationFieldsContainer.innerHTML = `<div class="mb-4"><label class="block text-gray-700 text-sm font-bold mb-2">وصف الدفعة</label><input type="text" id="opDesc" class="shadow w-full p-2 border rounded" required></div><div class="grid grid-cols-2 lg:grid-cols-3 gap-4"><div><label class="block text-sm mb-1">نوع الرصيد</label><select id="opPaymentType" class="shadow w-full p-2 border rounded"><option value="balanceGold">ذهب (جرام)</option><option value="balanceLaborEGP">مصنعية (ج.م)</option><option value="balanceLaborUSD">مصنعية ($)</option><option value="balanceDiamondUSD">ألماس ($)</option><option value="balanceEgyptianGoldUSD">ذهب مصري ($)</option></select></div><div id="paymentCurrencyWrapper" class="hidden"><label class="block text-sm mb-1">عملة السداد</label><select id="opPaymentCurrency" class="shadow w-full p-2 border rounded"><option value="USD">نفس العملة ($)</option><option value="EGP">جنيه مصري (EGP)</option></select></div><div id="exchangeRateWrapper" class="hidden"><label class="block text-sm mb-1">سعر الصرف</label><input type="number" step="0.01" id="opExchangeRate" class="shadow w-full p-2 border rounded"></div><div><label class="block text-sm mb-1">المبلغ/الكمية</label><input type="number" step="0.01" id="opPaymentAmount" class="shadow w-full p-2 border rounded" required></div></div>`;
                updatePaymentDescription();
            }
        });
        document.getElementById('operationForm').addEventListener('submit', async (e) => {
            e.preventDefault(); if (!currentOpenCustomerId) return;
            const operationType = operationTypeSelect.value;
            const description = document.getElementById('opDesc')?.value || 'عملية جديدة';
            const opDate = new Date();
            const customerRef = doc(db, 'artifacts', appId, 'users', currentUserId, 'customers', currentOpenCustomerId);
            try {
                 await runTransaction(db, async (transaction) => {
                    const customerDoc = await transaction.get(customerRef);
                    if (!customerDoc.exists()) throw "Customer document does not exist!";
                    const customerData = customerDoc.data();
                    let updates = {}; let transactionsToAdd = [];
                    if (operationType === 'delivery') {
                        const fields = { balanceGold: 'opGold', balanceLaborEGP: 'opLaborEGP', balanceLaborUSD: 'opLaborUSD', balanceDiamondUSD: 'opDiamondUSD' };
                        for (const [balanceType, elemId] of Object.entries(fields)) {
                            const amount = parseFloat(document.getElementById(elemId)?.value || 0);
                            if (amount > 0) {
                                updates[balanceType] = (customerData[balanceType] || 0) + amount;
                                transactionsToAdd.push({ description, type: 'debit', balanceType, amount, createdAt: opDate });
                            }
                        }
                    } else if (operationType === 'payment') {
                        let amount = parseFloat(document.getElementById('opPaymentAmount')?.value || 0);
                        const balanceType = document.getElementById('opPaymentType').value;
                        if (amount > 0) {
                            const paymentCurrency = document.getElementById('opPaymentCurrency')?.value;
                            const exchangeRate = parseFloat(document.getElementById('opExchangeRate')?.value || 0);
                            let creditAmount = amount; let transData = { description, type: 'credit', balanceType, createdAt: opDate };
                            if (balanceType.includes('USD') && paymentCurrency === 'EGP') {
                                if (exchangeRate <= 0) throw new Error("يجب إدخال سعر صرف صحيح.");
                                creditAmount = amount / exchangeRate;
                                transData.originalAmount = amount; transData.originalCurrency = 'EGP'; transData.exchangeRate = exchangeRate;
                            }
                            updates[balanceType] = (customerData[balanceType] || 0) - creditAmount;
                            transData.amount = creditAmount;
                            transactionsToAdd.push(transData);
                        }
                    } else throw new Error("Invalid operation type");
                    if(Object.keys(updates).length > 0) {
                        transaction.update(customerRef, updates);
                        transactionsToAdd.forEach(trans => transaction.set(doc(collection(customerRef, 'transactions')), trans));
                    }
                });
                console.log("Operation successfully committed!");
                document.getElementById('operationForm').reset();
                operationFieldsContainer.innerHTML = '';
                document.getElementById('operationModal').classList.add('hidden');
            } catch (error) { console.error("Operation failed: ", error); alert(`فشلت العملية. ${error.message}`); }
        });

        // Expenses Section
        function loadExpenses() {
            if (!currentUserId) return;
            const q = query(collection(db, 'artifacts', appId, 'users', currentUserId, 'general_expenses'), orderBy("createdAt", "desc"));
            onSnapshot(q, (snapshot) => {
                const tableBody = document.getElementById('expensesTableBody');
                tableBody.innerHTML = '';
                if(snapshot.empty) { tableBody.innerHTML = '<tr><td colspan="3" class="text-center p-4">لا توجد مصروفات مسجلة.</td></tr>'; return; }
                snapshot.forEach(doc => {
                    const expense = doc.data();
                    const row = `<tr class="bg-white border-b"><td class="px-6 py-4">${new Date(expense.createdAt.seconds * 1000).toLocaleDateString('ar-EG')}</td><td class="px-6 py-4">${expense.description}</td><td class="px-6 py-4 font-bold">${formatCurrency(expense.amount, expense.currency)}</td></tr>`;
                    tableBody.innerHTML += row;
                });
                loadDashboardData(); 
            }, error => { console.error("Error loading expenses:", error); });
        }
        document.getElementById('addExpenseBtn').addEventListener('click', () => { document.getElementById('expenseDate').valueAsDate = new Date(); document.getElementById('expenseModal').classList.remove('hidden'); });
        document.getElementById('closeExpenseModal').addEventListener('click', () => document.getElementById('expenseModal').classList.add('hidden'));
        document.getElementById('expenseForm')?.addEventListener('change', (e) => {
            if(e.target.id === 'expenseCurrency') {
                 document.getElementById('expenseExchangeRateWrapper').classList.toggle('hidden', e.target.value !== 'USD');
            }
        });
        document.getElementById('expenseForm').addEventListener('submit', async(e) => {
            e.preventDefault(); if (!currentUserId) return;
            const currency = document.getElementById('expenseCurrency').value;
            const amount = parseFloat(document.getElementById('expenseAmount').value);
            const newExpense = { description: document.getElementById('expenseDesc').value, amount, currency, createdAt: new Date(document.getElementById('expenseDate').value) };
            try { await addDoc(collection(db, 'artifacts', appId, 'users', currentUserId, 'general_expenses'), newExpense); document.getElementById('expenseForm').reset(); document.getElementById('expenseModal').classList.add('hidden'); } catch (error) { console.error("Error adding expense:", error); }
        });
        
        // Workers Section
        function loadWorkers() {
            if (!currentUserId) return;
            const q = query(collection(db, 'artifacts', appId, 'users', currentUserId, 'workers'), orderBy("name"));
            onSnapshot(q, (snapshot) => {
                allWorkersData = snapshot.docs;
                const tableBody = document.getElementById('workersTableBody');
                const workerSelect = document.getElementById('orderWorker');
                tableBody.innerHTML = '';
                workerSelect.innerHTML = '<option value="">-- لم يعين --</option>';
                if(snapshot.empty) { tableBody.innerHTML = '<tr><td colspan="5" class="text-center p-4">لم يتم إضافة أي عمال.</td></tr>'; return; }
                snapshot.forEach(doc => {
                    const worker = doc.data();
                    const row = `<tr class="bg-white border-b"><td class="px-6 py-4">${worker.name}</td><td class="px-6 py-4">${worker.nationality}</td><td class="px-6 py-4 font-bold ${getBalanceColor(worker.balanceGold)}">${formatNumber(worker.balanceGold)}</td><td class="px-6 py-4 font-bold ${getBalanceColor(worker.balanceEGP)}">${formatCurrency(worker.balanceEGP)}</td><td class="px-6 py-4"><button data-id="${doc.id}" class="view-worker-details-btn text-blue-600 hover:underline">عرض التفاصيل</button></td></tr>`;
                    tableBody.innerHTML += row;
                    workerSelect.innerHTML += `<option value="${doc.id}">${worker.name}</option>`;
                });
                loadDashboardData();
                 document.querySelectorAll('.view-worker-details-btn').forEach(button => button.addEventListener('click', (e) => navigateToWorkerDetail(e.target.dataset.id)));
            }, error => { console.error("Error loading workers:", error); });
        }
        document.getElementById('addWorkerBtn').addEventListener('click', () => {
            document.getElementById('workerModal').classList.remove('hidden');
            document.getElementById('workerForm').reset();
            document.getElementById('workerStartDate').valueAsDate = new Date();
        });
        document.getElementById('closeWorkerModal').addEventListener('click', () => document.getElementById('workerModal').classList.add('hidden'));
        document.getElementById('workerForm').addEventListener('submit', async (e) => {
            e.preventDefault(); if (!currentUserId) return;
            const newWorker = { 
                name: document.getElementById('workerName').value, 
                nationality: document.getElementById('workerNationality').value, 
                salaryEGP: parseFloat(document.getElementById('workerSalaryEGP').value) || 0,
                salaryUSD: parseFloat(document.getElementById('workerSalaryUSD').value) || 0,
                startDate: new Date(document.getElementById('workerStartDate').value),
                notes: document.getElementById('workerNotes').value,
                balanceGold: 0, balanceEGP: 0, balanceUSD: 0, 
                createdAt: new Date() 
            };
            try { await addDoc(collection(db, 'artifacts', appId, 'users', currentUserId, 'workers'), newWorker); document.getElementById('workerForm').reset(); document.getElementById('workerModal').classList.add('hidden'); } catch (error) { console.error("Error adding worker:", error); }
        });
        
        // Worker Detail Page
        function navigateToWorkerDetail(workerId) {
            const workerDoc = allWorkersData.find(doc => doc.id === workerId);
            if (!workerDoc) return;
            currentOpenWorkerId = workerId;
            renderWorkerDetailPage(workerDoc.data());
            loadWorkerTransactions(workerId);
            document.getElementById('workers').classList.add('hidden');
            document.getElementById('worker-detail-page').classList.remove('hidden');
        }
        function renderWorkerDetailPage(worker) {
            document.getElementById('workerDetailName').textContent = worker.name;
            document.getElementById('workerDetailNationality').textContent = worker.nationality;
            const infoContainer = document.getElementById('workerInfoSummary');
            infoContainer.innerHTML = `
                <div class="bg-white p-4 rounded-xl shadow-md"><h4 class="text-gray-500 text-sm">تاريخ البدء</h4><p class="text-xl font-bold">${new Date(worker.startDate.seconds * 1000).toLocaleDateString('ar-EG')}</p></div>
                <div class="bg-white p-4 rounded-xl shadow-md"><h4 class="text-gray-500 text-sm">الراتب (ج.م)</h4><p class="text-xl font-bold">${formatCurrency(worker.salaryEGP)}</p></div>
                <div class="bg-white p-4 rounded-xl shadow-md"><h4 class="text-gray-500 text-sm">الراتب ($)</h4><p class="text-xl font-bold">${formatCurrency(worker.salaryUSD, 'USD')}</p></div>
                <div class="bg-white p-4 rounded-xl shadow-md col-span-1 lg:col-span-1"><h4 class="text-gray-500 text-sm">ملاحظات</h4><p class="text-md">${worker.notes || 'لا يوجد'}</p></div>
            `;
            const summaryContainer = document.getElementById('workerBalanceSummary');
            summaryContainer.innerHTML = `
                <div class="bg-white p-4 rounded-xl shadow-md border-l-4 ${getBalanceColor(worker.balanceGold)}"><h4 class="text-gray-500 text-sm">رصيد ذهب (عهدة)</h4><p class="text-2xl font-bold">${formatNumber(worker.balanceGold)} جرام</p></div>
                <div class="bg-white p-4 rounded-xl shadow-md border-l-4 ${getBalanceColor(worker.balanceEGP)}"><h4 class="text-gray-500 text-sm">رصيد نقدي (ج.م)</h4><p class="text-2xl font-bold">${formatCurrency(worker.balanceEGP)}</p></div>
                <div class="bg-white p-4 rounded-xl shadow-md border-l-4 ${getBalanceColor(worker.balanceUSD)}"><h4 class="text-gray-500 text-sm">رصيد نقدي ($)</h4><p class="text-2xl font-bold">${formatCurrency(worker.balanceUSD, 'USD')}</p></div>
            `;
        }
         function loadWorkerTransactions(workerId) {
            if (unsubscribeWorkerTransactions) unsubscribeWorkerTransactions();
            const q = query(collection(db, 'artifacts', appId, 'users', currentUserId, 'workers', workerId, 'transactions'), orderBy("createdAt", "desc"));
            unsubscribeWorkerTransactions = onSnapshot(q, (snapshot) => {
                const tableBody = document.getElementById('workerTransactionsTableBody');
                tableBody.innerHTML = '';
                if(snapshot.empty) { tableBody.innerHTML = '<tr><td colspan="6" class="text-center p-4">لا توجد حركات.</td></tr>'; return; }
                const workerDoc = allWorkersData.find(doc => doc.id === workerId);
                let runningBalances = workerDoc ? { ...workerDoc.data() } : {};
                const transactions = snapshot.docs.map(doc => doc.data());
                for (const trans of transactions) {
                    const isDebit = trans.type === 'debit';
                    const currency = trans.balanceType === 'balanceGold' ? null : trans.balanceType.slice(-3).toUpperCase();
                    const amountFormatted = currency ? formatCurrency(trans.amount, currency) : `${formatNumber(trans.amount)} جرام`;
                    const balanceTypeArabic = { balanceGold: 'ذهب', balanceEGP: 'نقدي (ج.م)', balanceUSD: 'نقدي ($)'}[trans.balanceType];
                    const runningBalanceFormatted = currency ? formatCurrency(runningBalances[trans.balanceType], currency) : `${formatNumber(runningBalances[trans.balanceType])} جرام`;
                    const row = `<tr class="bg-white border-b"><td class="px-4 py-3">${new Date(trans.createdAt.seconds * 1000).toLocaleDateString('ar-EG')}</td><td class="px-4 py-3">${trans.description}</td><td class="px-4 py-3 text-red-600 font-semibold">${isDebit ? amountFormatted : '-'}</td><td class="px-4 py-3 text-green-600 font-semibold">${!isDebit ? amountFormatted : '-'}</td><td class="px-4 py-3 font-bold ${getBalanceColor(runningBalances[trans.balanceType])}">${runningBalanceFormatted}</td><td class="px-4 py-3">${balanceTypeArabic}</td></tr>`;
                    tableBody.innerHTML += row;
                    if (isDebit) runningBalances[trans.balanceType] -= trans.amount; else runningBalances[trans.balanceType] += trans.amount;
                }
            });
        }
        document.getElementById('backToWorkersBtn').addEventListener('click', () => {
            if (unsubscribeWorkerTransactions) unsubscribeWorkerTransactions();
            currentOpenWorkerId = null;
            document.getElementById('worker-detail-page').classList.add('hidden');
            document.getElementById('workers').classList.remove('hidden');
        });
        
        // Worker Operation Modal
        const workerOperationTypeSelect = document.getElementById('workerOperationType');
        const workerOperationFieldsContainer = document.getElementById('workerOperationFieldsContainer');
        document.getElementById('addWorkerOperationBtn').addEventListener('click', () => {
             document.getElementById('workerOperationModal').classList.remove('hidden');
             document.getElementById('workerOperationForm').reset();
             workerOperationFieldsContainer.innerHTML = '';
        });
        document.getElementById('closeWorkerOperationModal').addEventListener('click', () => document.getElementById('workerOperationModal').classList.add('hidden'));
        workerOperationTypeSelect.addEventListener('change', (e) => {
            const type = e.target.value;
            workerOperationFieldsContainer.innerHTML = '';
            if (type === 'work_assignment') {
                 workerOperationFieldsContainer.innerHTML = `<div class="mb-4 relative"><label class="block text-gray-700 text-sm font-bold mb-2">وصف العمل</label><input type="text" id="workerOpDesc" class="shadow w-full p-2 border rounded" required placeholder="مثال: عهدة طقم جديد"><button type="button" id="suggestWorkerOpDescBtn" class="absolute left-2 top-9 text-xs bg-purple-100 text-purple-700 px-2 py-1 rounded">✨ اقتراح</button></div><div class="grid grid-cols-2 gap-4"><div><label class="block text-sm mb-1">ذهب (جرام)</label><input type="number" step="0.01" id="workerOpGold" class="shadow w-full p-2 border rounded" placeholder="0"></div><div><label class="block text-sm mb-1">عيار</label><input type="number" id="workerOpCarat" class="shadow w-full p-2 border rounded" placeholder="21"></div></div>`;
            } else if (type === 'payment') {
                workerOperationFieldsContainer.innerHTML = `<div class="mb-4"><label class="block text-gray-700 text-sm font-bold mb-2">وصف الدفعة</label><input type="text" id="workerOpDesc" class="shadow w-full p-2 border rounded" required placeholder="مثال: دفعة من الراتب"></div><div class="grid grid-cols-2 gap-4"><div><label class="block text-sm mb-1">نوع التسوية</label><select id="workerOpPaymentType" class="shadow w-full p-2 border rounded"><option value="balanceEGP">نقدية (ج.م)</option><option value="balanceUSD">نقدية ($)</option><option value="balanceGold">ذهب (جرام)</option></select></div><div><label class="block text-sm mb-1">المبلغ / الكمية</label><input type="number" step="0.01" id="workerOpPaymentAmount" class="shadow w-full p-2 border rounded" required></div></div>`;
            }
        });
         document.getElementById('workerOperationForm').addEventListener('submit', async (e) => {
            e.preventDefault(); if (!currentOpenWorkerId) return;
            const operationType = workerOperationTypeSelect.value;
            const description = document.getElementById('workerOpDesc')?.value || 'عملية جديدة';
            const opDate = new Date();
            const workerRef = doc(db, 'artifacts', appId, 'users', currentUserId, 'workers', currentOpenWorkerId);
            try {
                 await runTransaction(db, async (transaction) => {
                    const workerDoc = await transaction.get(workerRef);
                    if (!workerDoc.exists()) throw "Worker document does not exist!";
                    const workerData = workerDoc.data();
                    let updates = {}; let transactionsToAdd = [];
                    if (operationType === 'work_assignment') {
                        const goldAmount = parseFloat(document.getElementById('workerOpGold')?.value || 0);
                        if (goldAmount > 0) {
                            updates.balanceGold = (workerData.balanceGold || 0) + goldAmount;
                            transactionsToAdd.push({ description, type: 'debit', balanceType: 'balanceGold', amount: goldAmount, createdAt: opDate, carat: document.getElementById('workerOpCarat')?.value });
                        }
                    } else if (operationType === 'payment') {
                        const amount = parseFloat(document.getElementById('workerOpPaymentAmount')?.value || 0);
                        const balanceType = document.getElementById('workerOpPaymentType').value;
                        if (amount > 0) {
                            updates[balanceType] = (workerData[balanceType] || 0) - amount;
                            transactionsToAdd.push({ description, type: 'credit', balanceType, amount, createdAt: opDate });
                        }
                    } else throw new Error("Invalid operation type");
                    if(Object.keys(updates).length > 0) {
                        transaction.update(workerRef, updates);
                        transactionsToAdd.forEach(trans => transaction.set(doc(collection(workerRef, 'transactions')), trans));
                    }
                });
                console.log("Worker Operation successfully committed!");
                document.getElementById('workerOperationForm').reset();
                workerOperationFieldsContainer.innerHTML = '';
                document.getElementById('workerOperationModal').classList.add('hidden');
            } catch (error) { console.error("Worker Operation failed: ", error); alert(`فشلت العملية. ${error.message}`); }
        });
        
        // Production Orders Section
        function loadProductionOrders() {
            if (!currentUserId) return;
            const q = query(collection(db, 'artifacts', appId, 'users', currentUserId, 'production_orders'), orderBy("createdAt", "desc"));
            onSnapshot(q, (snapshot) => {
                const tableBody = document.getElementById('ordersTableBody');
                tableBody.innerHTML = '';
                if(snapshot.empty) { tableBody.innerHTML = '<tr><td colspan="6" class="text-center p-4">لا توجد أوامر تشغيل.</td></tr>'; return; }
                snapshot.forEach(doc => {
                    const order = doc.data();
                    const customer = allCustomersData.find(c => c.id === order.customerId)?.data() || {name: 'غير محدد'};
                    const worker = allWorkersData.find(w => w.id === order.workerId)?.data() || {name: 'لم يعين'};
                    const statusColors = { 'جديد': 'bg-blue-100 text-blue-800', 'قيد التنفيذ': 'bg-yellow-100 text-yellow-800', 'تم التسليم': 'bg-green-100 text-green-800' };
                    const row = `
                        <tr class="bg-white border-b">
                            <td class="px-6 py-4">${new Date(order.createdAt.seconds * 1000).toLocaleDateString('ar-EG')}</td>
                            <td class="px-6 py-4">${customer.name}</td>
                            <td class="px-6 py-4">${order.description}</td>
                            <td class="px-6 py-4">${order.weight || '-'}</td>
                            <td class="px-6 py-4">${worker.name}</td>
                            <td class="px-6 py-4"><span class="px-2 py-1 font-semibold leading-tight rounded-full ${statusColors[order.status] || 'bg-gray-100 text-gray-800'}">${order.status}</span></td>
                        </tr>
                    `;
                    tableBody.innerHTML += row;
                });
            }, error => { console.error("Error loading production orders:", error); });
        }
        document.getElementById('addOrderBtn').addEventListener('click', () => {
            document.getElementById('orderModal').classList.remove('hidden');
            document.getElementById('orderForm').reset();
            document.getElementById('orderDate').valueAsDate = new Date();
        });
        document.getElementById('closeOrderModal').addEventListener('click', () => document.getElementById('orderModal').classList.add('hidden'));
        document.getElementById('orderForm').addEventListener('submit', async (e) => {
            e.preventDefault(); if (!currentUserId) return;
            const newOrder = {
                customerId: document.getElementById('orderCustomer').value,
                workerId: document.getElementById('orderWorker').value,
                createdAt: new Date(document.getElementById('orderDate').value),
                item: document.getElementById('orderItem').value,
                description: document.getElementById('orderDescription').value,
                carat: parseInt(document.getElementById('orderCarat').value) || 0,
                weight: parseFloat(document.getElementById('orderWeight').value) || 0,
                quantity: parseInt(document.getElementById('orderQuantity').value) || 1,
                notes: document.getElementById('orderNotes').value,
                status: 'جديد'
            };
            try {
                await addDoc(collection(db, 'artifacts', appId, 'users', currentUserId, 'production_orders'), newOrder);
                document.getElementById('orderForm').reset();
                document.getElementById('orderModal').classList.add('hidden');
            } catch (error) { console.error("Error adding order:", error); }
        });

        // Casting Section
        function loadCastingJobs() {
            if (!currentUserId) return;
            const q = query(collection(db, 'artifacts', appId, 'users', currentUserId, 'casting_jobs'), orderBy("createdAt", "desc"));
            onSnapshot(q, (snapshot) => {
                const tableBody = document.getElementById('castingTableBody');
                tableBody.innerHTML = '';
                if(snapshot.empty) { tableBody.innerHTML = '<tr><td colspan="6" class="text-center p-4">لا توجد عمليات سباكة مسجلة.</td></tr>'; return; }
                snapshot.forEach(doc => {
                    const job = doc.data();
                    const waste = (job.weightSent || 0) - (job.weightReturned || 0);
                    const row = `
                        <tr class="bg-white border-b">
                            <td class="px-6 py-4">${new Date(job.createdAt.seconds * 1000).toLocaleDateString('ar-EG')}</td>
                            <td class="px-6 py-4">${job.description}</td>
                            <td class="px-6 py-4">${formatNumber(job.weightSent)}</td>
                            <td class="px-6 py-4">${formatNumber(job.weightReturned)}</td>
                            <td class="px-6 py-4 text-red-600 font-bold">${formatNumber(waste)}</td>
                            <td class="px-6 py-4">${job.lossCalculation || '-'}</td>
                        </tr>
                    `;
                    tableBody.innerHTML += row;
                });
            }, error => { console.error("Error loading casting jobs:", error); });
        }
        document.getElementById('addCastingBtn').addEventListener('click', () => {
            document.getElementById('castingModal').classList.remove('hidden');
            document.getElementById('castingForm').reset();
            document.getElementById('castingDate').valueAsDate = new Date();
        });
        document.getElementById('closeCastingModal').addEventListener('click', () => document.getElementById('castingModal').classList.add('hidden'));
        document.getElementById('castingForm').addEventListener('submit', async (e) => {
             e.preventDefault(); if (!currentUserId) return;
             const newJob = {
                createdAt: new Date(document.getElementById('castingDate').value),
                description: document.getElementById('castingDescription').value,
                weightSent: parseFloat(document.getElementById('castingWeightSent').value) || 0,
                weightReturned: parseFloat(document.getElementById('castingWeightReturned').value) || 0,
                lossCalculation: document.getElementById('castingLossCalculation').value,
             };
             try {
                await addDoc(collection(db, 'artifacts', appId, 'users', currentUserId, 'casting_jobs'), newJob);
                document.getElementById('castingForm').reset();
                document.getElementById('castingModal').classList.add('hidden');
            } catch (error) { console.error("Error adding casting job:", error); }
        });
        
        // Polishing Section
        function loadPolishingJobs() {
            if (!currentUserId) return;
            const q = query(collection(db, 'artifacts', appId, 'users', currentUserId, 'polishing_jobs'), orderBy("createdAt", "desc"));
            onSnapshot(q, (snapshot) => {
                const tableBody = document.getElementById('polishingTableBody');
                tableBody.innerHTML = '';
                if(snapshot.empty) { tableBody.innerHTML = '<tr><td colspan="4" class="text-center p-4">لا توجد عمليات تلميع مسجلة.</td></tr>'; return; }
                snapshot.forEach(doc => {
                    const job = doc.data();
                    const row = `<tr class="bg-white border-b"><td class="px-6 py-4">${new Date(job.createdAt.seconds * 1000).toLocaleDateString('ar-EG')}</td><td class="px-6 py-4">${job.description}</td><td class="px-6 py-4">${formatNumber(job.weightDebit)}</td><td class="px-6 py-4">${formatNumber(job.weightCredit)}</td></tr>`;
                    tableBody.innerHTML += row;
                });
            }, error => { console.error("Error loading polishing jobs:", error); });
        }
        document.getElementById('addPolishingBtn').addEventListener('click', () => {
            document.getElementById('polishingModal').classList.remove('hidden');
            document.getElementById('polishingForm').reset();
            document.getElementById('polishingDate').valueAsDate = new Date();
        });
        document.getElementById('closePolishingModal').addEventListener('click', () => document.getElementById('polishingModal').classList.add('hidden'));
        document.getElementById('polishingForm').addEventListener('submit', async (e) => {
             e.preventDefault(); if (!currentUserId) return;
             const newJob = {
                createdAt: new Date(document.getElementById('polishingDate').value),
                description: document.getElementById('polishingDescription').value,
                weightDebit: parseFloat(document.getElementById('polishingWeightDebit').value) || 0,
                weightCredit: parseFloat(document.getElementById('polishingWeightCredit').value) || 0,
             };
             try {
                await addDoc(collection(db, 'artifacts', appId, 'users', currentUserId, 'polishing_jobs'), newJob);
                document.getElementById('polishingForm').reset();
                document.getElementById('polishingModal').classList.add('hidden');
            } catch (error) { console.error("Error adding polishing job:", error); }
        });

        // Filing Loss Section
        function loadFilingLoss() {
            if (!currentUserId) return;
            const q = query(collection(db, 'artifacts', appId, 'users', currentUserId, 'filing_loss'), orderBy("createdAt", "desc"));
            onSnapshot(q, (snapshot) => {
                const tableBody = document.getElementById('filingLossTableBody');
                tableBody.innerHTML = '';
                if(snapshot.empty) { tableBody.innerHTML = '<tr><td colspan="3" class="text-center p-4">لا توجد خسيات مسجلة.</td></tr>'; return; }
                snapshot.forEach(doc => {
                    const job = doc.data();
                    const row = `<tr class="bg-white border-b"><td class="px-6 py-4">${new Date(job.createdAt.seconds * 1000).toLocaleDateString('ar-EG')}</td><td class="px-6 py-4">${job.description}</td><td class="px-6 py-4">${formatNumber(job.weight)}</td></tr>`;
                    tableBody.innerHTML += row;
                });
            }, error => { console.error("Error loading filing loss:", error); });
        }
        document.getElementById('addFilingLossBtn').addEventListener('click', () => {
            document.getElementById('filingLossModal').classList.remove('hidden');
            document.getElementById('filingLossForm').reset();
            document.getElementById('filingLossDate').valueAsDate = new Date();
        });
        document.getElementById('closeFilingLossModal').addEventListener('click', () => document.getElementById('filingLossModal').classList.add('hidden'));
        document.getElementById('filingLossForm').addEventListener('submit', async (e) => {
             e.preventDefault(); if (!currentUserId) return;
             const newJob = {
                createdAt: new Date(document.getElementById('filingLossDate').value),
                description: document.getElementById('filingLossDescription').value,
                weight: parseFloat(document.getElementById('filingLossWeight').value) || 0,
             };
             try {
                await addDoc(collection(db, 'artifacts', appId, 'users', currentUserId, 'filing_loss'), newJob);
                document.getElementById('filingLossForm').reset();
                document.getElementById('filingLossModal').classList.add('hidden');
            } catch (error) { console.error("Error adding filing loss job:", error); }
        });


        // Gemini API Features
        const geminiModal = document.getElementById('geminiModal');
        document.getElementById('generateMessageBtn').addEventListener('click', async () => {
            if (!currentOpenCustomerId) return;
            geminiModal.classList.remove('hidden');
            const geminiResultContainer = document.getElementById('geminiResultContainer');
            geminiResultContainer.innerHTML = '<div class="flex justify-center items-center h-full"><div class="loader"></div></div>';
            const customerDoc = allCustomersData.find(doc => doc.id === currentOpenCustomerId);
            if (!customerDoc) { geminiResultContainer.innerHTML = '<p class="text-red-500">لم يتم العثور على العميل.</p>'; return; }
            const customer = customerDoc.data();
            let balanceSummary = `Customer Name: ${customer.name}. Balances: `;
            let hasDebt = false;
            Object.entries(customer).forEach(([key, value]) => { if(key.startsWith('balance') && value > 0) { balanceSummary += `${formatNumber(value)} in ${key}, `; hasDebt=true; } });
            if (!hasDebt) balanceSummary = `Customer Name: ${customer.name} has a zero or positive balance.`;
            const prompt = `You are an assistant for a jewelry workshop owner in Egypt. A customer named ${customer.name} has the following account balance situation: ${balanceSummary}. Based on this, generate a polite and professional follow-up message in ARABIC. If the customer owes money, gently remind them. If they don't, thank them. Keep it friendly. Start with "مرحباً، ${customer.name}".`;
            try {
                const apiKey = ""; const apiUrl = `https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent?key=${apiKey}`;
                const response = await fetch(apiUrl, { method: 'POST', headers: { 'Content-Type': 'application/json' }, body: JSON.stringify({ contents: [{ parts: [{ text: prompt }] }] }) });
                if (!response.ok) throw new Error(`API call failed: ${response.status}`);
                const result = await response.json();
                if (result.candidates && result.candidates.length > 0) { const text = result.candidates[0].content.parts[0].text; geminiResultContainer.innerHTML = `<textarea id="geminiMessageText" class="w-full h-full bg-transparent border-0 focus:ring-0 resize-none">${text}</textarea>`; } else throw new Error("No content.");
            } catch (error) { console.error("Gemini API Error:", error); geminiResultContainer.innerHTML = `<p class="text-red-500">حدث خطأ. ${error.message}</p>`; }
        });
        document.getElementById('closeGeminiModal').addEventListener('click', () => document.getElementById('geminiModal').classList.add('hidden'));
        document.getElementById('copyGeminiMessageBtn').addEventListener('click', () => {
            const messageText = document.getElementById('geminiMessageText');
            if (messageText) {
                messageText.select();
                document.execCommand('copy');
                const btn = document.getElementById('copyGeminiMessageBtn');
                btn.textContent = 'تم النسخ!';
                setTimeout(() => { btn.textContent = 'نسخ'; }, 2000);
            }
        });
        document.getElementById('analyzePerformanceBtn').addEventListener('click', async () => {
            const analysisModal = document.getElementById('dashboardAnalysisModal');
            const analysisContainer = document.getElementById('analysisResultContainer');
            analysisModal.classList.remove('hidden');
            analysisContainer.innerHTML = '<div class="flex justify-center items-center h-full"><div class="loader"></div></div>';
            
            const totalExpenses = document.getElementById('kpiTotalExpenses').textContent;
            const totalWages = document.getElementById('kpiTotalWages').textContent;

            const prompt = `You are a financial analyst for a small jewelry workshop in Egypt. Here is the financial summary for the current month: Total General Expenses: ${totalExpenses}, Total Worker Wages: ${totalWages}. Please provide a brief analysis in ARABIC. Highlight the main cost centers (general vs. wages). Provide one actionable suggestion to potentially optimize these costs or improve financial tracking. Keep it concise and easy for a business owner to understand.`;
             try {
                const apiKey = ""; const apiUrl = `https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent?key=${apiKey}`;
                const response = await fetch(apiUrl, { method: 'POST', headers: { 'Content-Type': 'application/json' }, body: JSON.stringify({ contents: [{ parts: [{ text: prompt }] }] }) });
                if (!response.ok) throw new Error(`API call failed: ${response.status}`);
                const result = await response.json();
                if (result.candidates && result.candidates.length > 0) { const text = result.candidates[0].content.parts[0].text; analysisContainer.innerHTML = `<div class="prose max-w-none">${text.replace(/\n/g, '<br>')}</div>`; } else throw new Error("No content.");
            } catch (error) { console.error("Gemini API Error:", error); analysisContainer.innerHTML = `<p class="text-red-500">حدث خطأ. ${error.message}</p>`; }
        });
        document.getElementById('closeAnalysisModal').addEventListener('click', () => document.getElementById('dashboardAnalysisModal').classList.add('hidden'));
        document.getElementById('expenseForm')?.addEventListener('change', (e) => {
            if(e.target.id === 'expenseCurrency') {
                 document.getElementById('expenseExchangeRateWrapper').classList.toggle('hidden', e.target.value !== 'USD');
            }
        });
        document.getElementById('suggestDescBtn')?.addEventListener('click', async (e) => {
             const input = document.getElementById('expenseDesc');
             if(!input.value) return;
             const btn = e.target;
             btn.textContent = '...';
             const prompt = `Based on the following keyword for an expense in a jewelry workshop, suggest a full, clear description in Arabic. Keyword: "${input.value}"`;
             try {
                const apiKey = ""; const apiUrl = `https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent?key=${apiKey}`;
                const response = await fetch(apiUrl, { method: 'POST', headers: { 'Content-Type': 'application/json' }, body: JSON.stringify({ contents: [{ parts: [{ text: prompt }] }] }) });
                if (!response.ok) throw new Error(`API call failed: ${response.status}`);
                const result = await response.json();
                if (result.candidates && result.candidates.length > 0) {
                    input.value = result.candidates[0].content.parts[0].text.trim();
                } else throw new Error("No content.");
            } catch (error) { console.error("Gemini API Error:", error); }
            finally { btn.textContent = '✨ اقتراح';}
        });
        operationFieldsContainer.addEventListener('click', async (e) => {
            if (e.target.id === 'suggestCustomerOpDescBtn') {
                const btn = e.target;
                const opGold = document.getElementById('opGold')?.value || 0; const opLaborEGP = document.getElementById('opLaborEGP')?.value || 0; const opLaborUSD = document.getElementById('opLaborUSD')?.value || 0; const opDiamondUSD = document.getElementById('opDiamondUSD')?.value || 0;
                const descInput = document.getElementById('opDesc');
                if (!opGold && !opLaborEGP && !opLaborUSD && !opDiamondUSD) { descInput.value = "الرجاء إدخال بعض تفاصيل الشغل أولاً."; return; }
                let details = [];
                if(opGold > 0) details.push(`${opGold} جرام ذهب`); if(opLaborEGP > 0) details.push(`مصنعية ${opLaborEGP} جنيه`); if(opLaborUSD > 0) details.push(`مصنعية ${opLaborUSD} دولار`); if(opDiamondUSD > 0) details.push(`قيمة ألماس ${opDiamondUSD} دولار`);
                const prompt = `Based on the following work delivery details for a customer, generate a concise and clear transaction description in Arabic. Details: ${details.join(', ')}. The description should be suitable for an accounting ledger. For example: "تسليم شغل يحتوي على ${details.join(' و ')}".`;
                btn.textContent = '...'; btn.disabled = true;
                try {
                    const apiKey = ""; const apiUrl = `https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent?key=${apiKey}`;
                    const response = await fetch(apiUrl, { method: 'POST', headers: { 'Content-Type': 'application/json' }, body: JSON.stringify({ contents: [{ parts: [{ text: prompt }] }] }) });
                    if (!response.ok) throw new Error(`API call failed: ${response.status}`);
                    const result = await response.json();
                    if (result.candidates && result.candidates.length > 0) { descInput.value = result.candidates[0].content.parts[0].text.trim().replace(/"/g, ''); } else throw new Error("No content.");
                } catch (error) { console.error("Gemini API Error:", error); descInput.value = "فشل الاقتراح."; }
                finally { btn.textContent = '✨ اقتراح'; btn.disabled = false;}
            }
        });
        workerOperationFieldsContainer.addEventListener('click', async (e) => {
            if (e.target.id === 'suggestWorkerOpDescBtn') {
                const btn = e.target;
                const goldWeight = document.getElementById('workerOpGold')?.value; const goldCarat = document.getElementById('workerOpCarat')?.value;
                const descInput = document.getElementById('workerOpDesc');
                if (!goldWeight) { descInput.value = "الرجاء إدخال وزن الذهب أولاً."; return; }
                const prompt = `Based on the following work assignment to a worker, generate a concise and clear transaction description in Arabic. Details: ${goldWeight}g of ${goldCarat || 21}-carat gold. The description should be suitable for an accounting ledger. For example: "عهدة ذهب عيار ${goldCarat || 21} وزن ${goldWeight} جرام".`;
                btn.textContent = '...'; btn.disabled = true;
                try {
                    const apiKey = ""; const apiUrl = `https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent?key=${apiKey}`;
                    const response = await fetch(apiUrl, { method: 'POST', headers: { 'Content-Type': 'application/json' }, body: JSON.stringify({ contents: [{ parts: [{ text: prompt }] }] }) });
                    if (!response.ok) throw new Error(`API call failed: ${response.status}`);
                    const result = await response.json();
                    if (result.candidates && result.candidates.length > 0) { descInput.value = result.candidates[0].content.parts[0].text.trim().replace(/"/g, ''); } else throw new Error("No content.");
                } catch (error) { console.error("Gemini API Error:", error); descInput.value = "فشل الاقتراح."; }
                finally { btn.textContent = '✨ اقتراح'; btn.disabled = false; }
            }
        });

        // Main Navigation Logic
        const navLinks = document.querySelectorAll('.nav-link');
        const pageSections = document.querySelectorAll('.page-section');
        navLinks.forEach(link => {
            link.addEventListener('click', (e) => {
                e.preventDefault();
                const targetId = link.getAttribute('href');
                pageSections.forEach(section => section.classList.add('hidden'));
                const targetSection = document.querySelector(targetId);
                if (targetSection) targetSection.classList.remove('hidden');
                navLinks.forEach(navLink => navLink.classList.remove('bg-[#E6DED5]', 'text-[#554639]', 'font-bold', 'shadow-sm'));
                link.classList.add('bg-[#E6DED5]', 'text-[#554639]', 'font-bold', 'shadow-sm');
            });
        });

        // Production Tabs Logic
        document.querySelectorAll('.production-tab').forEach(tab => {
            tab.addEventListener('click', () => {
                const targetId = tab.dataset.target;
                document.querySelectorAll('.production-content').forEach(content => content.classList.add('hidden'));
                document.getElementById(targetId).classList.remove('hidden');
                document.querySelectorAll('.production-tab').forEach(t => t.classList.replace('tab-active', 'tab-inactive'));
                tab.classList.replace('tab-inactive', 'tab-active');
            });
        });
    </script>
</body>
</html>
