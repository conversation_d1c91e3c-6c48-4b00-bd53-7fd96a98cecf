<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام إدارة ورشة الذهب - MySQL</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@400;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            scroll-behavior: smooth;
        }
        .chart-container {
            position: relative;
            width: 100%;
            max-width: 800px;
            margin-left: auto;
            margin-right: auto;
            height: 350px;
            max-height: 40vh;
        }
        ::-webkit-scrollbar {
            width: 8px;
        }
        ::-webkit-scrollbar-track {
            background: #f1f1f1;
        }
        ::-webkit-scrollbar-thumb {
            background: #c7a46f;
            border-radius: 10px;
        }
        ::-webkit-scrollbar-thumb:hover {
            background: #b39362;
        }
        .modal-bg {
            background-color: rgba(0, 0, 0, 0.5);
            transition: opacity 0.3s ease;
        }
        .modal-content {
            transition: transform 0.3s ease;
        }
        .loader {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #c7a46f;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
         .tab-active {
            border-color: #c7a46f;
            color: #c7a46f;
        }
        .tab-inactive {
           border-color: transparent;
           color: #6b7280;
        }
        .tab-inactive:hover {
            border-color: #d1d5db;
            color: #4b5563;
        }
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-left: 8px;
        }
        .status-online {
            background-color: #10b981;
        }
        .status-offline {
            background-color: #ef4444;
        }
    </style>
</head>
<body class="bg-[#FDFBF6] text-[#4A4A4A]">

    <div id="loading-overlay" class="fixed inset-0 bg-white bg-opacity-90 flex justify-center items-center z-50">
        <div class="text-center">
            <div class="loader mx-auto mb-4"></div>
            <p class="text-lg font-semibold text-[#6B5B4B]">جار تحميل النظام...</p>
            <p id="loading-status" class="text-sm text-gray-500 mt-2">اختبار الاتصال بقاعدة البيانات...</p>
        </div>
    </div>

    <div class="flex h-screen bg-[#FDFBF6]">
        <!-- Sidebar -->
        <aside class="w-64 bg-[#F0EBE3] p-6 shadow-lg flex flex-col justify-between overflow-y-auto">
            <div>
                <h1 class="text-2xl font-bold text-[#6B5B4B] mb-2">ورشة الذهب</h1>
                <div class="text-xs text-gray-500 mb-6">
                    <p>حالة الاتصال:</p>
                    <p id="connection-status" class="select-all flex items-center">
                        <span class="status-indicator status-offline"></span>
                        غير متصل
                    </p>
                </div>
                <nav id="main-nav" class="space-y-3">
                    <a href="#dashboard" class="nav-link bg-[#E6DED5] text-[#554639] flex items-center p-3 rounded-lg font-bold shadow-sm">
                        <svg class="w-6 h-6 ml-3" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6"></path></svg>
                        لوحة التحكم
                    </a>
                    <a href="#production-stages" class="nav-link hover:bg-[#E6DED5] flex items-center p-3 rounded-lg transition-all duration-200">
                        <svg class="w-6 h-6 ml-3" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-2.11.732a6 6 0 01-3.86.517L6.17 15.5a2 2 0 01-1.022.547m0 0l-1.57-1.57a2 2 0 010-2.828l2.387-2.387a6 6 0 018.486 0l2.387 2.387a2 2 0 010 2.828l-1.57 1.57M9 17l.01 0"></path></svg>
                        مراحل الإنتاج
                    </a>
                    <a href="#inventory" class="nav-link hover:bg-[#E6DED5] flex items-center p-3 rounded-lg transition-all duration-200">
                        <svg class="w-6 h-6 ml-3" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 8h14M5 8a2 2 0 110-4h14a2 2 0 110 4M5 8v10a2 2 0 002 2h10a2 2 0 002-2V8m-9 4h4"></path></svg>
                        المخزون
                    </a>
                    <a href="#customers" class="nav-link hover:bg-[#E6DED5] flex items-center p-3 rounded-lg transition-all duration-200">
                        <svg class="w-6 h-6 ml-3" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path></svg>
                        العملاء
                    </a>
                     <a href="#expenses" class="nav-link hover:bg-[#E6DED5] flex items-center p-3 rounded-lg transition-all duration-200">
                        <svg class="w-6 h-6 ml-3" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 8h6m-5 4h.01M4.887 14.12l-1.396 1.396a2 2 0 000 2.828l1.396 1.396A2 2 0 006.586 20H17.414a2 2 0 001.697-.707l1.396-1.396a2 2 0 000-2.828l-1.396-1.396A2 2 0 0017.414 14H6.586a2 2 0 00-1.697.12z"></path></svg>
                        المصروفات
                    </a>
                     <a href="#workers" class="nav-link hover:bg-[#E6DED5] flex items-center p-3 rounded-lg transition-all duration-200">
                        <svg class="w-6 h-6 ml-3" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M12 12a4 4 0 110-8 4 4 0 010 8z"></path></svg>
                        العمال
                    </a>
                </nav>
            </div>
             <div class="text-center text-xs text-[#9c8b7b]">
                <p>&copy; 2025 نظام إدارة ورشة الذهب</p>
                <p>MySQL + Node.js</p>
            </div>
        </aside>

        <!-- Main Content -->
        <main class="flex-1 p-8 overflow-y-auto">
            
            <section id="dashboard" class="page-section">
                <header class="mb-8 flex justify-between items-center">
                    <div>
                        <h2 class="text-3xl font-bold text-[#6B5B4B]">لوحة التحكم</h2>
                        <p class="text-md text-[#9c8b7b]">نظرة عامة وشاملة على أداء الورشة.</p>
                    </div>
                    <button id="refreshDataBtn" class="bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white font-bold py-2 px-4 rounded-lg transition flex items-center">
                        🔄 تحديث البيانات
                    </button>
                </header>
                 <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                    <div class="bg-white p-6 rounded-xl shadow-md border-l-4 border-[#c7a46f]">
                        <h3 class="text-gray-500 text-sm font-semibold">إجمالي العمال</h3>
                        <p id="kpiTotalWorkers" class="text-3xl font-bold text-[#6B5B4B] mt-2">0</p>
                    </div>
                    <div class="bg-white p-6 rounded-xl shadow-md border-l-4 border-blue-500">
                        <h3 class="text-gray-500 text-sm font-semibold">إجمالي العملاء</h3>
                        <p id="kpiTotalCustomers" class="text-3xl font-bold text-blue-600 mt-2">0</p>
                    </div>
                    <div class="bg-white p-6 rounded-xl shadow-md border-l-4 border-red-500">
                        <h3 class="text-gray-500 text-sm font-semibold">إجمالي المصروفات</h3>
                        <p id="kpiTotalExpenses" class="text-3xl font-bold text-red-600 mt-2">0 ج.م</p>
                    </div>
                    <div class="bg-white p-6 rounded-xl shadow-md border-l-4 border-green-500">
                        <h3 class="text-gray-500 text-sm font-semibold">أوامر الإنتاج</h3>
                        <p id="kpiTotalOrders" class="text-3xl font-bold text-green-600 mt-2">0</p>
                    </div>
                </div>
                <div class="bg-white p-6 rounded-xl shadow-md">
                    <h3 class="text-xl font-bold text-[#6B5B4B] mb-4">ملخص المصروفات</h3>
                    <div class="chart-container">
                        <canvas id="expensesChart"></canvas>
                    </div>
                </div>
            </section>
            
            <!-- باقي الصفحات ستضاف هنا -->
            <section id="workers" class="page-section hidden">
                <header class="mb-8">
                    <h2 class="text-3xl font-bold text-[#6B5B4B]">إدارة العمال</h2>
                    <p class="text-md text-[#9c8b7b]">إدارة بيانات العمال وحساباتهم المالية.</p>
                </header>
                <div class="bg-white p-6 rounded-xl shadow-md">
                    <div class="flex justify-between items-center mb-4">
                        <h3 class="text-xl font-bold text-[#6B5B4B]">قائمة العمال</h3>
                        <button id="addWorkerBtn" class="bg-[#c7a46f] hover:bg-[#b39362] text-white font-bold py-2 px-4 rounded-lg transition">إضافة عامل جديد</button>
                    </div>
                    <div class="overflow-x-auto">
                        <table class="w-full text-sm text-left text-gray-500">
                            <thead class="text-xs text-gray-700 uppercase bg-gray-50">
                                <tr>
                                    <th scope="col" class="px-6 py-3">اسم العامل</th>
                                    <th scope="col" class="px-6 py-3">الجنسية</th>
                                    <th scope="col" class="px-6 py-3">رصيد ذهب (جرام)</th>
                                    <th scope="col" class="px-6 py-3">رصيد نقدي (ج.م)</th>
                                    <th scope="col" class="px-6 py-3">الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody id="workersTableBody">
                                <tr>
                                    <td colspan="5" class="text-center p-4">جار تحميل البيانات...</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </section>

        </main>
    </div>

    <!-- تحميل API Client -->
    <script src="js/api-client.js"></script>
    
    <!-- تحميل التطبيق الرئيسي -->
    <script src="js/app-mysql.js"></script>

</body>
</html>
