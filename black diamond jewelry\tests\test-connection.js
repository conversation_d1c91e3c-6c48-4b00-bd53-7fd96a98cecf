const mysql = require('mysql2/promise');

async function testConnection() {
    console.log('🔍 اختبار الاتصال بقاعدة البيانات...');
    
    // إعدادات الاتصال المباشرة
    const config = {
        host: 'localhost',
        user: 'root',
        password: '2452329511',
        database: 'black_diamond_jewelry',
        port: 3306
    };

    console.log('🔧 إعدادات الاتصال:');
    console.log('Host:', config.host);
    console.log('User:', config.user);
    console.log('Database:', config.database);
    console.log('Port:', config.port);
    console.log('Password:', config.password ? '***موجودة***' : '❌غير موجودة');
    
    try {
        console.log('📡 محاولة الاتصال...');
        const connection = await mysql.createConnection(config);
        console.log('✅ تم الاتصال بنجاح!');
        
        // اختبار استعلام
        const [rows] = await connection.execute('SELECT DATABASE() as current_db');
        console.log('📊 قاعدة البيانات الحالية:', rows[0].current_db);
        
        // عرض الجداول
        const [tables] = await connection.execute('SHOW TABLES');
        console.log('📋 الجداول الموجودة:');
        tables.forEach(table => {
            console.log('  ✓', Object.values(table)[0]);
        });
        
        // اختبار جدول العمال
        const [workers] = await connection.execute('SELECT COUNT(*) as count FROM workers');
        console.log('👥 عدد العمال:', workers[0].count);
        
        await connection.end();
        console.log('🔒 تم إغلاق الاتصال');
        
        return true;
        
    } catch (error) {
        console.error('❌ خطأ في الاتصال:');
        console.error('📝 الرسالة:', error.message);
        console.error('🔢 الكود:', error.code);
        return false;
    }
}

testConnection().then(success => {
    if (success) {
        console.log('\n🎉 قاعدة البيانات تعمل بشكل مثالي!');
    } else {
        console.log('\n⚠️ هناك مشكلة في الاتصال');
    }
});
