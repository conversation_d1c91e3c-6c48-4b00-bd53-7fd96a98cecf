/**
 * API Client للتواصل مع Node.js Backend
 * يحل محل Firebase ويتصل بقاعدة البيانات MySQL
 */

class APIClient {
    constructor() {
        this.baseURL = 'http://localhost:3000/api';
        this.isOnline = navigator.onLine;
        this.setupOfflineHandling();
    }

    setupOfflineHandling() {
        window.addEventListener('online', () => {
            this.isOnline = true;
            console.log('🌐 الاتصال بالإنترنت متاح');
        });

        window.addEventListener('offline', () => {
            this.isOnline = false;
            console.log('📴 وضع عدم الاتصال');
        });
    }

    async request(endpoint, options = {}) {
        const url = `${this.baseURL}${endpoint}`;
        const config = {
            headers: {
                'Content-Type': 'application/json',
                ...options.headers
            },
            ...options
        };

        try {
            const response = await fetch(url, config);
            
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            
            return await response.json();
        } catch (error) {
            console.error(`API Error for ${endpoint}:`, error);
            
            if (!this.isOnline) {
                throw new Error('لا يوجد اتصال بالإنترنت');
            }
            
            throw error;
        }
    }

    // ==================== Workers API ====================
    async getWorkers() {
        return await this.request('/workers');
    }

    async getWorker(id) {
        return await this.request(`/workers/${id}`);
    }

    async createWorker(workerData) {
        return await this.request('/workers', {
            method: 'POST',
            body: JSON.stringify(workerData)
        });
    }

    async updateWorker(id, workerData) {
        return await this.request(`/workers/${id}`, {
            method: 'PUT',
            body: JSON.stringify(workerData)
        });
    }

    async deleteWorker(id) {
        return await this.request(`/workers/${id}`, {
            method: 'DELETE'
        });
    }

    // ==================== Customers API ====================
    async getCustomers() {
        return await this.request('/customers');
    }

    async getCustomer(id) {
        return await this.request(`/customers/${id}`);
    }

    async createCustomer(customerData) {
        return await this.request('/customers', {
            method: 'POST',
            body: JSON.stringify(customerData)
        });
    }

    async updateCustomer(id, customerData) {
        return await this.request(`/customers/${id}`, {
            method: 'PUT',
            body: JSON.stringify(customerData)
        });
    }

    async deleteCustomer(id) {
        return await this.request(`/customers/${id}`, {
            method: 'DELETE'
        });
    }

    // ==================== Raw Materials API ====================
    async getRawMaterials() {
        return await this.request('/raw-materials');
    }

    async createRawMaterial(materialData) {
        return await this.request('/raw-materials', {
            method: 'POST',
            body: JSON.stringify(materialData)
        });
    }

    // ==================== Expenses API ====================
    async getExpenses() {
        return await this.request('/expenses');
    }

    async createExpense(expenseData) {
        return await this.request('/expenses', {
            method: 'POST',
            body: JSON.stringify(expenseData)
        });
    }

    // ==================== Production Orders API ====================
    async getProductionOrders() {
        return await this.request('/production-orders');
    }

    async createProductionOrder(orderData) {
        return await this.request('/production-orders', {
            method: 'POST',
            body: JSON.stringify(orderData)
        });
    }

    // ==================== Transactions API ====================
    async getCustomerTransactions(customerId) {
        return await this.request(`/customers/${customerId}/transactions`);
    }

    async createCustomerTransaction(customerId, transactionData) {
        return await this.request(`/customers/${customerId}/transactions`, {
            method: 'POST',
            body: JSON.stringify(transactionData)
        });
    }

    async getWorkerTransactions(workerId) {
        return await this.request(`/workers/${workerId}/transactions`);
    }

    async createWorkerTransaction(workerId, transactionData) {
        return await this.request(`/workers/${workerId}/transactions`, {
            method: 'POST',
            body: JSON.stringify(transactionData)
        });
    }

    // ==================== Casting Jobs API ====================
    async getCastingJobs() {
        return await this.request('/casting-jobs');
    }

    async createCastingJob(castingData) {
        return await this.request('/casting-jobs', {
            method: 'POST',
            body: JSON.stringify(castingData)
        });
    }

    // ==================== Polishing Jobs API ====================
    async getPolishingJobs() {
        return await this.request('/polishing-jobs');
    }

    async createPolishingJob(polishingData) {
        return await this.request('/polishing-jobs', {
            method: 'POST',
            body: JSON.stringify(polishingData)
        });
    }

    // ==================== Filing Loss Jobs API ====================
    async getFilingLossJobs() {
        return await this.request('/filing-loss-jobs');
    }

    async createFilingLossJob(filingLossData) {
        return await this.request('/filing-loss-jobs', {
            method: 'POST',
            body: JSON.stringify(filingLossData)
        });
    }

    // ==================== Database Tables API ====================
    async getTables() {
        return await this.request('/tables');
    }

    // ==================== Utility Methods ====================
    formatCurrency(value, currency = 'EGP') {
        return new Intl.NumberFormat('ar-EG', {
            style: 'currency',
            currency: currency,
            minimumFractionDigits: 2
        }).format(value || 0);
    }

    formatNumber(value) {
        return new Intl.NumberFormat('ar-EG', {
            minimumFractionDigits: 2,
            maximumFractionDigits: 2
        }).format(value || 0);
    }

    getBalanceColor(value) {
        return value >= 0 ? 'text-green-600' : 'text-red-600';
    }

    formatDate(date) {
        return new Date(date).toLocaleDateString('ar-EG');
    }
}

// إنشاء instance عام للاستخدام
window.apiClient = new APIClient();
