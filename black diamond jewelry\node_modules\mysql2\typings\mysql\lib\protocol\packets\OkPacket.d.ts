/**
 * @deprecated
 * `OkPacket` is deprecated and might be removed in the future major release. Please use `ResultSetHeader` instead.
 */
declare interface OkPacket {
  constructor: {
    name: 'OkPacket';
  };
  fieldCount: number;
  affectedRows: number;
  /**
   * @deprecated
   * `changedRows` is deprecated and might be removed in the future major release. Please use `affectedRows` property instead.
   */
  changedRows: number;
  insertId: number;
  serverStatus: number;
  warningCount: number;
  message: string;
  protocol41: boolean;
}

export { OkPacket };
