{"name": "black-diamond-jewelry", "version": "1.0.0", "description": "نظام إدارة ورشة الذهب والماس", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "electron": "electron electron-main.js", "electron-dev": "NODE_ENV=development electron electron-main.js", "build-electron": "electron-builder", "dist": "electron-builder --publish=never", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["jewelry", "workshop", "accounting", "mysql", "electron"], "author": "Black Diamond Jewelry Workshop", "license": "MIT", "type": "commonjs", "dependencies": {"cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^5.1.0", "mysql2": "^3.14.1", "pg": "^8.16.2", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.1"}, "devDependencies": {"electron": "^28.3.3", "electron-builder": "^24.9.1", "nodemon": "^3.0.2"}, "optionalDependencies": {"electron-store": "^8.2.0"}}