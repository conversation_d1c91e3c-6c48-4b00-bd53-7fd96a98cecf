const mysql = require('mysql2/promise');
delete require.cache[require.resolve('dotenv')];
require('dotenv').config();

async function testDatabaseConnection() {
    console.log('اختبار الاتصال بقاعدة البيانات...');
    console.log('معلومات الاتصال:');
    console.log('Host:', process.env.DB_HOST);
    console.log('User:', process.env.DB_USER);
    console.log('Database:', process.env.DB_DATABASE);
    console.log('Port:', process.env.DB_PORT);
    
    try {
        // محاولة الاتصال بدون تجمع الاتصالات أولاً
        const connection = await mysql.createConnection({
            host: process.env.DB_HOST,
            user: process.env.DB_USER,
            password: process.env.DB_PASSWORD,
            database: process.env.DB_DATABASE,
            port: parseInt(process.env.DB_PORT)
        });
        
        console.log('✅ تم الاتصال بقاعدة البيانات بنجاح!');
        
        // اختبار استعلام بسيط
        const [rows] = await connection.execute('SELECT 1 as test');
        console.log('✅ تم تنفيذ الاستعلام بنجاح:', rows);
        
        // عرض الجداول الموجودة
        const [tables] = await connection.execute('SHOW TABLES');
        console.log('📋 الجداول الموجودة في قاعدة البيانات:');
        tables.forEach(table => {
            console.log('  -', Object.values(table)[0]);
        });
        
        // اختبار جدول العمال
        const [workers] = await connection.execute('SELECT COUNT(*) as worker_count FROM workers');
        console.log('👥 عدد العمال في قاعدة البيانات:', workers[0].worker_count);
        
        await connection.end();
        console.log('✅ تم إغلاق الاتصال بنجاح');
        
    } catch (error) {
        console.error('❌ خطأ في الاتصال بقاعدة البيانات:');
        console.error('رسالة الخطأ:', error.message);
        console.error('كود الخطأ:', error.code);
        console.error('رقم الخطأ:', error.errno);
    }
}

testDatabaseConnection();
