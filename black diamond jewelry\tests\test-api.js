const fetch = (...args) => import('node-fetch').then(({default: fetch}) => fetch(...args));

async function testAPI() {
    const baseURL = 'http://localhost:3000/api';
    
    console.log('🧪 اختبار API...');
    
    try {
        // اختبار إضافة عامل
        console.log('\n1. إضافة عامل جديد...');
        const workerData = {
            name: 'أحمد محمد',
            nationality: 'مصري',
            balance_gold: 0,
            balance_egp: 0
        };
        
        const workerResponse = await fetch(`${baseURL}/workers`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(workerData)
        });
        
        if (workerResponse.ok) {
            const worker = await workerResponse.json();
            console.log('✅ تم إضافة العامل:', worker);
        } else {
            console.log('❌ فشل في إضافة العامل:', await workerResponse.text());
        }
        
        // اختبار إضافة عميل
        console.log('\n2. إضافة عميل جديد...');
        const customerData = {
            name: 'محمد علي',
            contact: '01234567890',
            balance_gold: 0,
            balance_labor_egp: 0,
            balance_labor_usd: 0,
            balance_diamond_usd: 0,
            balance_egyptian_gold_usd: 0
        };
        
        const customerResponse = await fetch(`${baseURL}/customers`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(customerData)
        });
        
        if (customerResponse.ok) {
            const customer = await customerResponse.json();
            console.log('✅ تم إضافة العميل:', customer);
        } else {
            console.log('❌ فشل في إضافة العميل:', await customerResponse.text());
        }
        
        // اختبار إضافة مادة خام
        console.log('\n3. إضافة مادة خام...');
        const materialData = {
            code: 'GOLD21',
            name: 'ذهب عيار 21',
            unit: 'جرام',
            stock: 100
        };
        
        const materialResponse = await fetch(`${baseURL}/raw-materials`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(materialData)
        });
        
        if (materialResponse.ok) {
            const material = await materialResponse.json();
            console.log('✅ تم إضافة المادة الخام:', material);
        } else {
            console.log('❌ فشل في إضافة المادة الخام:', await materialResponse.text());
        }
        
        // اختبار قراءة البيانات
        console.log('\n4. قراءة جميع العمال...');
        const workersResponse = await fetch(`${baseURL}/workers`);
        if (workersResponse.ok) {
            const workers = await workersResponse.json();
            console.log('✅ العمال:', workers);
        }
        
        console.log('\n5. قراءة جميع العملاء...');
        const customersResponse = await fetch(`${baseURL}/customers`);
        if (customersResponse.ok) {
            const customers = await customersResponse.json();
            console.log('✅ العملاء:', customers);
        }
        
        console.log('\n6. قراءة المواد الخام...');
        const materialsResponse = await fetch(`${baseURL}/raw-materials`);
        if (materialsResponse.ok) {
            const materials = await materialsResponse.json();
            console.log('✅ المواد الخام:', materials);
        }
        
        console.log('\n🎉 تم اختبار API بنجاح!');
        
    } catch (error) {
        console.error('❌ خطأ في اختبار API:', error);
    }
}

// تشغيل الاختبار
testAPI();
