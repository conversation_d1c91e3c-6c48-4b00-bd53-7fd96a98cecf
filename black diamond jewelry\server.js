// Import required packages
const express = require('express');
const cors = require('cors');
const mysql = require('mysql2/promise');
const swaggerJSDoc = require('swagger-jsdoc');
const swaggerUi = require('swagger-ui-express');
require('dotenv').config();

// Debug environment variables
console.log('Environment variables loaded:');
console.log('DB_HOST:', process.env.DB_HOST);
console.log('DB_USER:', process.env.DB_USER);
console.log('DB_DATABASE:', process.env.DB_DATABASE);
console.log('DB_PASSWORD:', process.env.DB_PASSWORD ? '***' : 'NOT SET');
console.log('DB_PORT:', process.env.DB_PORT);

// Create an Express application
const app = express();
const port = 3000;

// Middleware
app.use(cors());
app.use(express.json());

// MySQL Database Connection Pool
const pool = mysql.createPool({
  host: 'localhost',
  user: 'root',
  database: 'black_diamond_jewelry',
  password: '2452329511',
  port: 3306,
  waitForConnections: true,
  connectionLimit: 10,
  queueLimit: 0
});

// Test database connection
async function testConnection() {
  try {
    const connection = await pool.getConnection();
    console.log('Connected to MySQL database successfully!');
    connection.release();
  } catch (err) {
    console.error('Error acquiring database connection:', err.message);
    console.error('Connection details:', {
      host: process.env.DB_HOST,
      user: process.env.DB_USER,
      database: process.env.DB_DATABASE,
      port: process.env.DB_PORT
    });
  }
}
testConnection();

// Swagger Configuration
const swaggerOptions = {
  swaggerDefinition: {
    openapi: '3.0.0',
    info: {
      title: 'Jewelry Workshop API',
      version: '1.0.0',
      description: 'API for managing the jewelry workshop operations',
    },
    servers: [
      {
        url: `http://localhost:${port}`,
        description: 'Development server'
      }
    ]
  },
  apis: ['./server.js'], // Path to the API docs
};
const swaggerDocs = swaggerJSDoc(swaggerOptions);
app.use('/api-docs', swaggerUi.serve, swaggerUi.setup(swaggerDocs));

// =================================================================
// ==                  API Endpoints: Workers                     ==
// =================================================================

/**
 * @swagger
 * /api/workers:
 *   get:
 *     summary: Retrieve a list of all workers
 *     description: Fetches a list of all workers from the database, ordered by name.
 *     responses:
 *       200:
 *         description: A list of workers.
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 type: object
 */
app.get('/api/workers', async (req, res) => {
    try {
        const [rows] = await pool.query('SELECT * FROM workers ORDER BY name');
        res.json(rows);
    } catch (err) {
        console.error(err.message);
        res.status(500).send("Server Error");
    }
});

/**
 * @swagger
 * /api/workers/{id}:
 *   get:
 *     summary: Retrieve a single worker by ID
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         description: Numeric ID of the worker to retrieve.
 *         schema:
 *           type: integer
 *     responses:
 *       200:
 *         description: A single worker object.
 *       404:
 *         description: Worker not found.
 */
app.get('/api/workers/:id', async (req, res) => {
    try {
        const { id } = req.params;
        const [rows] = await pool.query('SELECT * FROM workers WHERE id = ?', [id]);
        if (rows.length === 0) {
            return res.status(404).json({ msg: 'Worker not found' });
        }
        res.json(rows[0]);
    } catch (err) {
        console.error(err.message);
        res.status(500).send('Server Error');
    }
});

/**
 * @swagger
 * /api/workers:
 *   post:
 *     summary: Create a new worker
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               name:
 *                 type: string
 *               nationality:
 *                 type: string
 *               start_date:
 *                 type: string
 *                 format: date
 *               salary_egp:
 *                 type: number
 *               salary_usd:
 *                 type: number
 *               notes:
 *                 type: string
 *     responses:
 *       201:
 *         description: Worker created successfully.
 */
app.post('/api/workers', async (req, res) => {
    try {
        const { name, nationality, start_date, salary_egp, salary_usd, notes } = req.body;
        const [result] = await pool.query(
            'INSERT INTO workers (name, nationality, start_date, salary_egp, salary_usd, notes, balance_gold, balance_egp, balance_usd) VALUES (?, ?, ?, ?, ?, ?, 0, 0, 0)',
            [name, nationality, start_date, salary_egp, salary_usd, notes]
        );
        res.status(201).json({ id: result.insertId, name, nationality, start_date, salary_egp, salary_usd, notes });
    } catch (err) {
        console.error(err.message);
        res.status(500).send('Server Error');
    }
});

/**
 * @swagger
 * /api/workers/{id}:
 *   put:
 *     summary: Update an existing worker
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               name:
 *                 type: string
 *               salary_egp:
 *                 type: number
 *     responses:
 *       200:
 *         description: Worker updated successfully.
 *       404:
 *         description: Worker not found.
 */
app.put('/api/workers/:id', async (req, res) => {
    try {
        const { id } = req.params;
        const { name, nationality, start_date, salary_egp, salary_usd, notes } = req.body;
        const [result] = await pool.query(
            'UPDATE workers SET name = ?, nationality = ?, start_date = ?, salary_egp = ?, salary_usd = ?, notes = ? WHERE id = ?',
            [name, nationality, start_date, salary_egp, salary_usd, notes, id]
        );
        if (result.affectedRows === 0) {
            return res.status(404).json({ msg: 'Worker not found' });
        }
        res.json({ id, name, nationality, start_date, salary_egp, salary_usd, notes });
    } catch (err) {
        console.error(err.message);
        res.status(500).send('Server Error');
    }
});

/**
 * @swagger
 * /api/workers/{id}:
 *   delete:
 *     summary: Delete a worker
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *     responses:
 *       200:
 *         description: Worker deleted successfully.
 *       404:
 *         description: Worker not found.
 */
app.delete('/api/workers/:id', async (req, res) => {
    try {
        const { id } = req.params;
        const [result] = await pool.query('DELETE FROM workers WHERE id = ?', [id]);
        if (result.affectedRows === 0) {
            return res.status(404).json({ msg: 'Worker not found' });
        }
        res.json({ msg: 'Worker deleted' });
    } catch (err) {
        console.error(err.message);
        res.status(500).send('Server Error');
    }
});
// =================================================================
// ==                  API Endpoints: Customers                   ==
// =================================================================

/**
 * @swagger
 * /api/customers:
 *   get:
 *     summary: Get all customers
 */
app.get('/api/customers', async (req, res) => {
    try {
        const [rows] = await pool.query('SELECT * FROM customers ORDER BY name');
        res.json(rows);
    } catch (err) {
        console.error(err.message);
        res.status(500).send('Server Error');
    }
});

app.get('/api/customers/:id', async (req, res) => {
    try {
        const { id } = req.params;
        const [rows] = await pool.query('SELECT * FROM customers WHERE id = ?', [id]);
        if (rows.length === 0) {
            return res.status(404).json({ msg: 'Customer not found' });
        }
        res.json(rows[0]);
    } catch (err) {
        console.error(err.message);
        res.status(500).send('Server Error');
    }
});

app.post('/api/customers', async (req, res) => {
    try {
        const { name, contact, balance_gold, balance_labor_egp, balance_labor_usd, balance_diamond_usd, balance_egyptian_gold_usd } = req.body;
        const [result] = await pool.query(
            'INSERT INTO customers (name, contact, balance_gold, balance_labor_egp, balance_labor_usd, balance_diamond_usd, balance_egyptian_gold_usd) VALUES (?, ?, ?, ?, ?, ?, ?)',
            [name, contact, balance_gold || 0, balance_labor_egp || 0, balance_labor_usd || 0, balance_diamond_usd || 0, balance_egyptian_gold_usd || 0]
        );
        res.status(201).json({ id: result.insertId, name, contact });
    } catch (err) {
        console.error(err.message);
        res.status(500).send('Server Error');
    }
});

// =================================================================
// ==                  API Endpoints: Raw Materials              ==
// =================================================================

app.get('/api/raw-materials', async (req, res) => {
    try {
        const [rows] = await pool.query('SELECT * FROM raw_materials ORDER BY name');
        res.json(rows);
    } catch (err) {
        console.error(err.message);
        res.status(500).send('Server Error');
    }
});

app.post('/api/raw-materials', async (req, res) => {
    try {
        const { code, name, unit, stock } = req.body;
        const [result] = await pool.query(
            'INSERT INTO raw_materials (code, name, unit, stock) VALUES (?, ?, ?, ?)',
            [code, name, unit, stock || 0]
        );
        res.status(201).json({ id: result.insertId, code, name, unit, stock });
    } catch (err) {
        console.error(err.message);
        res.status(500).send('Server Error');
    }
});

// =================================================================
// ==                  API Endpoints: Tables                      ==
// =================================================================

/**
 * @swagger
 * /api/tables:
 *   get:
 *     summary: Get all table names in the database
 *     description: Returns a list of all table names in the public schema.
 *     responses:
 *       200:
 *         description: A list of table names.
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 type: string
 */
app.get('/api/tables', async (req, res) => {
    try {
        const [rows] = await pool.query('SHOW TABLES');
        res.json(rows.map(row => Object.values(row)[0]));
    } catch (err) {
        console.error(err.message);
        res.status(500).send('Server Error');
    }
});

// =================================================================
// ==                  API Endpoints: Expenses                   ==
// =================================================================

app.get('/api/expenses', async (req, res) => {
    try {
        const [rows] = await pool.query('SELECT * FROM general_expenses ORDER BY created_at DESC');
        res.json(rows);
    } catch (err) {
        console.error(err.message);
        res.status(500).send('Server Error');
    }
});

app.post('/api/expenses', async (req, res) => {
    try {
        const { description, amount, currency } = req.body;
        const [result] = await pool.query(
            'INSERT INTO general_expenses (description, amount, currency) VALUES (?, ?, ?)',
            [description, amount, currency || 'EGP']
        );
        res.status(201).json({ id: result.insertId, description, amount, currency });
    } catch (err) {
        console.error(err.message);
        res.status(500).send('Server Error');
    }
});

// =================================================================
// ==                  API Endpoints: Production Orders          ==
// =================================================================

app.get('/api/production-orders', async (req, res) => {
    try {
        const [rows] = await pool.query(`
            SELECT po.*, c.name as customer_name, w.name as worker_name
            FROM production_orders po
            LEFT JOIN customers c ON po.customer_id = c.id
            LEFT JOIN workers w ON po.worker_id = w.id
            ORDER BY po.created_at DESC
        `);
        res.json(rows);
    } catch (err) {
        console.error(err.message);
        res.status(500).send('Server Error');
    }
});

app.post('/api/production-orders', async (req, res) => {
    try {
        const { customer_id, worker_id, item, description, carat, weight, quantity, notes, status } = req.body;
        const [result] = await pool.query(
            'INSERT INTO production_orders (customer_id, worker_id, item, description, carat, weight, quantity, notes, status) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)',
            [customer_id, worker_id, item, description, carat, weight, quantity || 1, notes, status || 'جديد']
        );
        res.status(201).json({ id: result.insertId, customer_id, worker_id, item, description });
    } catch (err) {
        console.error(err.message);
        res.status(500).send('Server Error');
    }
});

// =================================================================
// ==                  API Endpoints: Casting Jobs              ==
// =================================================================

app.get('/api/casting-jobs', async (req, res) => {
    try {
        const [rows] = await pool.query('SELECT * FROM casting_jobs ORDER BY created_at DESC');
        res.json(rows);
    } catch (err) {
        console.error(err.message);
        res.status(500).send('Server Error');
    }
});

app.post('/api/casting-jobs', async (req, res) => {
    try {
        const { description, weight_sent, weight_returned, loss_calculation } = req.body;
        const [result] = await pool.query(
            'INSERT INTO casting_jobs (description, weight_sent, weight_returned, loss_calculation) VALUES (?, ?, ?, ?)',
            [description, weight_sent, weight_returned, loss_calculation]
        );
        res.status(201).json({ id: result.insertId, description, weight_sent, weight_returned });
    } catch (err) {
        console.error(err.message);
        res.status(500).send('Server Error');
    }
});

// =================================================================
// ==                  API Endpoints: Polishing Jobs            ==
// =================================================================

app.get('/api/polishing-jobs', async (req, res) => {
    try {
        const [rows] = await pool.query('SELECT * FROM polishing_jobs ORDER BY created_at DESC');
        res.json(rows);
    } catch (err) {
        console.error(err.message);
        res.status(500).send('Server Error');
    }
});

app.post('/api/polishing-jobs', async (req, res) => {
    try {
        const { description, weight_debit, weight_credit } = req.body;
        const [result] = await pool.query(
            'INSERT INTO polishing_jobs (description, weight_debit, weight_credit) VALUES (?, ?, ?)',
            [description, weight_debit || 0, weight_credit || 0]
        );
        res.status(201).json({ id: result.insertId, description, weight_debit, weight_credit });
    } catch (err) {
        console.error(err.message);
        res.status(500).send('Server Error');
    }
});

// =================================================================
// ==                  API Endpoints: Filing Loss Jobs          ==
// =================================================================

app.get('/api/filing-loss-jobs', async (req, res) => {
    try {
        const [rows] = await pool.query('SELECT * FROM filing_loss_jobs ORDER BY created_at DESC');
        res.json(rows);
    } catch (err) {
        console.error(err.message);
        res.status(500).send('Server Error');
    }
});

app.post('/api/filing-loss-jobs', async (req, res) => {
    try {
        const { description, weight } = req.body;
        const [result] = await pool.query(
            'INSERT INTO filing_loss_jobs (description, weight) VALUES (?, ?)',
            [description, weight]
        );
        res.status(201).json({ id: result.insertId, description, weight });
    } catch (err) {
        console.error(err.message);
        res.status(500).send('Server Error');
    }
});

// Start the server
app.listen(port, () => {
  console.log(`Server running on port ${port}`);
});
