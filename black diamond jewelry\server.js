// Import required packages
const express = require('express');
const cors = require('cors');
const mysql = require('mysql2');
const swaggerJSDoc = require('swagger-jsdoc');
const swaggerUi = require('swagger-ui-express');
require('dotenv').config();

// Create an Express application
const app = express();
const port = 3000;

// Middleware
app.use(cors());
app.use(express.json());

// MySQL Database Connection Pool
const pool = mysql.createPool({
  host: process.env.DB_HOST,
  user: process.env.DB_USER,
  database: process.env.DB_DATABASE,
  password: process.env.DB_PASSWORD,
  port: process.env.DB_PORT,
  waitForConnections: true,
  connectionLimit: 10,
  queueLimit: 0
});

// Test database connection
pool.getConnection((err, connection) => {
  if (err) {
    return console.error('Error acquiring database connection', err.stack);
  }
  console.log('Connected to MySQL database!');
  connection.release();
});

// Swagger Configuration
const swaggerOptions = {
  swaggerDefinition: {
    openapi: '3.0.0',
    info: {
      title: 'Jewelry Workshop API',
      version: '1.0.0',
      description: 'API for managing the jewelry workshop operations',
    },
    servers: [
      {
        url: `http://localhost:${port}`,
        description: 'Development server'
      }
    ]
  },
  apis: ['./server.js'], // Path to the API docs
};
const swaggerDocs = swaggerJSDoc(swaggerOptions);
app.use('/api-docs', swaggerUi.serve, swaggerUi.setup(swaggerDocs));

// =================================================================
// ==                  API Endpoints: Workers                     ==
// =================================================================

/**
 * @swagger
 * /api/workers:
 *   get:
 *     summary: Retrieve a list of all workers
 *     description: Fetches a list of all workers from the database, ordered by name.
 *     responses:
 *       200:
 *         description: A list of workers.
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 type: object
 */
app.get('/api/workers', async (req, res) => {
    try {
        const result = await pool.query('SELECT * FROM workers ORDER BY name');
        res.json(result[0]);
    } catch (err) {
        console.error(err.message);
        res.status(500).send("Server Error");
    }
});

/**
 * @swagger
 * /api/workers/{id}:
 *   get:
 *     summary: Retrieve a single worker by ID
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         description: Numeric ID of the worker to retrieve.
 *         schema:
 *           type: integer
 *     responses:
 *       200:
 *         description: A single worker object.
 *       404:
 *         description: Worker not found.
 */
app.get('/api/workers/:id', async (req, res) => {
    try {
        const { id } = req.params;
        const result = await pool.query('SELECT * FROM workers WHERE id = ?', [id]);
        if (result[0].length === 0) {
            return res.status(404).json({ msg: 'Worker not found' });
        }
        res.json(result[0][0]);
    } catch (err) {
        console.error(err.message);
        res.status(500).send('Server Error');
    }
});

/**
 * @swagger
 * /api/workers:
 *   post:
 *     summary: Create a new worker
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               name:
 *                 type: string
 *               nationality:
 *                 type: string
 *               start_date:
 *                 type: string
 *                 format: date
 *               salary_egp:
 *                 type: number
 *               salary_usd:
 *                 type: number
 *               notes:
 *                 type: string
 *     responses:
 *       201:
 *         description: Worker created successfully.
 */
app.post('/api/workers', async (req, res) => {
    try {
        const { name, nationality, start_date, salary_egp, salary_usd, notes } = req.body;
        const newWorker = await pool.query(
            'INSERT INTO workers (name, nationality, start_date, salary_egp, salary_usd, notes, balance_gold, balance_egp, balance_usd) VALUES (?, ?, ?, ?, ?, ?, 0, 0, 0)',
            [name, nationality, start_date, salary_egp, salary_usd, notes]
        );
        res.status(201).json({ id: newWorker.insertId, name, nationality, start_date, salary_egp, salary_usd, notes });
    } catch (err) {
        console.error(err.message);
        res.status(500).send('Server Error');
    }
});

/**
 * @swagger
 * /api/workers/{id}:
 *   put:
 *     summary: Update an existing worker
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               name:
 *                 type: string
 *               salary_egp:
 *                 type: number
 *     responses:
 *       200:
 *         description: Worker updated successfully.
 *       404:
 *         description: Worker not found.
 */
app.put('/api/workers/:id', async (req, res) => {
     try {
        const { id } = req.params;
        const { name, nationality, start_date, salary_egp, salary_usd, notes } = req.body;
        const updatedWorker = await pool.query(
            'UPDATE workers SET name = ?, nationality = ?, start_date = ?, salary_egp = ?, salary_usd = ?, notes = ? WHERE id = ?',
            [name, nationality, start_date, salary_egp, salary_usd, notes, id]
        );
        if (updatedWorker.affectedRows === 0) {
            return res.status(404).json({ msg: 'Worker not found' });
        }
        res.json({ id, name, nationality, start_date, salary_egp, salary_usd, notes });
    } catch (err) {
        console.error(err.message);
        res.status(500).send('Server Error');
    }
});

/**
 * @swagger
 * /api/workers/{id}:
 *   delete:
 *     summary: Delete a worker
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *     responses:
 *       200:
 *         description: Worker deleted successfully.
 *       404:
 *         description: Worker not found.
 */
app.delete('/api/workers/:id', async (req, res) => {
    try {
        const { id } = req.params;
        const deleteOp = await pool.query('DELETE FROM workers WHERE id = ?', [id]);
        if (deleteOp.affectedRows === 0) {
            return res.status(404).json({ msg: 'Worker not found' });
        }
        res.json({ msg: 'Worker deleted' });
    } catch (err) {
        console.error(err.message);
        res.status(500).send('Server Error');
    }
});
// =================================================================
// ==                  API Endpoints: Tables                      ==
// =================================================================

/**
 * @swagger
 * /api/tables:
 *   get:
 *     summary: Get all table names in the database
 *     description: Returns a list of all table names in the public schema.
 *     responses:
 *       200:
 *         description: A list of table names.
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 type: string
 */
app.get('/api/tables', async (req, res) => {
    try {
        const result = await pool.query(`SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' ORDER BY table_name`);
        res.json(result[0].map(row => row.table_name));
    } catch (err) {
        console.error(err.message);
        res.status(500).send('Server Error');
    }
});

// Start the server
app.listen(port, () => {
  console.log(`Server running on port ${port}`);
});
