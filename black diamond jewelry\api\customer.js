// =================================================================
// ==                  Jewelry Workshop Backend                     ==
// =================================================================
// Author: Gemini
// Description: A complete Node.js server using Express.js to manage
// a jewelry workshop's data with a PostgreSQL database.
// Features: Full CRUD API for all modules, and interactive API
// documentation with Swagger.
// =================================================================

// Import required packages
const express = require('express');
const cors = require('cors');
const { Pool } = require('pg');
const swaggerJSDoc = require('swagger-jsdoc');
const swaggerUi = require('swagger-ui-express');
require('dotenv').config();

// Create an Express application
const app = express();
const port = 3000;

// Middleware
app.use(cors());
app.use(express.json());

// PostgreSQL Database Connection Pool
const pool = new Pool({
  user: process.env.DB_USER,
  host: process.env.DB_HOST,
  database: process.env.DB_DATABASE,
  password: process.env.DB_PASSWORD,
  port: process.env.DB_PORT,
});

// Test database connection
pool.connect((err, client, release) => {
  if (err) {
    return console.error('Error acquiring database client', err.stack);
  }
  console.log('Connected to PostgreSQL database!');
  release();
});

// Swagger Configuration
const swaggerOptions = {
  swaggerDefinition: {
    openapi: '3.0.0',
    info: {
      title: 'Jewelry Workshop API',
      version: '1.0.0',
      description: 'واجهة برمجة التطبيقات (API) لإدارة عمليات ورشة الذهب',
    },
    servers: [
      {
        url: `http://localhost:${port}`,
        description: 'خادم التطوير المحلي'
      }
    ],
    tags: [
        { name: 'Workers', description: 'إدارة العمال' },
        { name: 'Customers', description: 'إدارة العملاء' },
        { name: 'Production', description: 'إدارة الإنتاج' },
        { name: 'Inventory', description: 'إدارة المخزون' },
        { name: 'Expenses', description: 'إدارة المصروفات' }
    ]
  },
  apis: ['./server.js'], // Path to the API docs
};
const swaggerDocs = swaggerJSDoc(swaggerOptions);
app.use('/api-docs', swaggerUi.serve, swaggerUi.setup(swaggerDocs));


// =================================================================
// ==                  API Endpoints: Workers                     ==
// =================================================================

/**
 * @swagger
 * /api/workers:
 * get:
 * summary: جلب قائمة بكل العمال
 * tags: [Workers]
 * responses:
 * 200:
 * description: قائمة العمال.
 */
app.get('/api/workers', async (req, res) => {
    try {
        const result = await pool.query('SELECT * FROM workers ORDER BY name');
        res.json(result.rows);
    } catch (err) {
        res.status(500).json({ error: err.message });
    }
});

/**
 * @swagger
 * /api/workers:
 * post:
 * summary: إنشاء عامل جديد
 * tags: [Workers]
 * requestBody:
 * required: true
 * content:
 * application/json:
 * schema:
 * type: object
 * properties:
 * name: { type: 'string' }
 * nationality: { type: 'string' }
 * start_date: { type: 'string', format: 'date' }
 * salary_egp: { type: 'number' }
 * salary_usd: { type: 'number' }
 * notes: { type: 'string' }
 * responses:
 * 201:
 * description: تم إنشاء العامل بنجاح.
 */
app.post('/api/workers', async (req, res) => {
    try {
        const { name, nationality, start_date, salary_egp, salary_usd, notes } = req.body;
        const newWorker = await pool.query(
            'INSERT INTO workers (name, nationality, start_date, salary_egp, salary_usd, notes, balance_gold, balance_egp, balance_usd) VALUES ($1, $2, $3, $4, $5, $6, 0, 0, 0) RETURNING *',
            [name, nationality, start_date, salary_egp || 0, salary_usd || 0, notes]
        );
        res.status(201).json(newWorker.rows[0]);
    } catch (err) {
        res.status(500).json({ error: err.message });
    }
});

// =================================================================
// ==                API Endpoints: Customers                   ==
// =================================================================
/**
 * @swagger
 * /api/customers:
 * get:
 * summary: جلب قائمة بكل العملاء
 * tags: [Customers]
 * responses:
 * 200:
 * description: قائمة العملاء.
 */
app.get('/api/customers', async (req, res) => {
    try {
        const result = await pool.query('SELECT * FROM customers ORDER BY name');
        res.json(result.rows);
    } catch (err) {
        res.status(500).json({ error: err.message });
    }
});

/**
 * @swagger
 * /api/customers/{id}:
 * get:
 * summary: جلب عميل واحد بواسطة الـ ID
 * tags: [Customers]
 * parameters:
 * - in: path
 * name: id
 * required: true
 * schema:
 * type: integer
 * responses:
 * 200:
 * description: بيانات العميل.
 * 404:
 * description: العميل غير موجود.
 */
app.get('/api/customers/:id', async (req, res) => {
    try {
        const { id } = req.params;
        const result = await pool.query('SELECT * FROM customers WHERE id = $1', [id]);
        if (result.rows.length === 0) {
            return res.status(404).json({ msg: 'Customer not found' });
        }
        res.json(result.rows[0]);
    } catch (err) {
        res.status(500).json({ error: err.message });
    }
});

/**
 * @swagger
 * /api/customers:
 * post:
 * summary: إنشاء عميل جديد مع أرصدة افتتاحية
 * tags: [Customers]
 * requestBody:
 * required: true
 * content:
 * application/json:
 * schema:
 * type: object
 * responses:
 * 201:
 * description: تم إنشاء العميل وحركاته الافتتاحية بنجاح.
 */
app.post('/api/customers', async (req, res) => {
    const client = await pool.connect();
    try {
        await client.query('BEGIN'); // Start transaction
        const { name, contact, balance_gold, balance_labor_egp, balance_labor_usd, balance_diamond_usd, balance_egyptian_gold_usd } = req.body;
        
        const newCustomerRes = await client.query(
            'INSERT INTO customers (name, contact, balance_gold, balance_labor_egp, balance_labor_usd, balance_diamond_usd, balance_egyptian_gold_usd) VALUES ($1, $2, $3, $4, $5, $6, $7) RETURNING *',
            [name, contact, balance_gold || 0, balance_labor_egp || 0, balance_labor_usd || 0, balance_diamond_usd || 0, balance_egyptian_gold_usd || 0]
        );
        const newCustomer = newCustomerRes.rows[0];

        // Add opening balance transactions
        const openingBalances = { balanceGold: newCustomer.balance_gold, balanceLaborEGP: newCustomer.balance_labor_egp, balanceLaborUSD: newCustomer.balance_labor_usd, balanceDiamondUSD: newCustomer.balance_diamond_usd, balanceEgyptianGoldUSD: newCustomer.balance_egyptian_gold_usd };
        const insertPromises = [];
        for (const [balanceType, amount] of Object.entries(openingBalances)) {
            if (amount !== 0) {
                const query = 'INSERT INTO customer_transactions (customer_id, description, type, balance_type, amount, created_at) VALUES ($1, $2, $3, $4, $5, $6)';
                insertPromises.push(client.query(query, [newCustomer.id, "رصيد افتتاحي", "debit", balanceType, amount, new Date()]));
            }
        }
        await Promise.all(insertPromises);
        
        await client.query('COMMIT'); // Commit transaction
        res.status(201).json(newCustomer);
    } catch (err) {
        await client.query('ROLLBACK'); // Rollback transaction on error
        console.error(err.message);
        res.status(500).json({ error: err.message });
    } finally {
        client.release();
    }
});


// ... (Other endpoints will be added here)


// Start the server
app.listen(port, () => {
  console.log(`Server running on port ${port}`);
});
