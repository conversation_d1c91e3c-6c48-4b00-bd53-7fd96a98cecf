// =================================================================
// ==                  Jewelry Workshop Backend                     ==
// =================================================================
// Author: Gemini
// Description: A complete Node.js server using Express.js to manage
// a jewelry workshop's data with a PostgreSQL database.
// Features: Full CRUD API for all modules, and interactive API
// documentation with Swagger.
// =================================================================

// Import required packages
const express = require('express');
const cors = require('cors');
const path = require('path');
const mysql = require('mysql2/promise');
const swaggerJSDoc = require('swagger-jsdoc');
const swaggerUi = require('swagger-ui-express');
require('dotenv').config();

// Create an Express application
const app = express();
const port = 3000;

// Middleware
app.use(cors());
app.use(express.json());

// MySQL Database Connection Pool
const pool = mysql.createPool({
  host: 'localhost',
  user: 'root',
  database: 'black_diamond_jewelry',
  password: '2452329511',
  port: 3306,
  waitForConnections: true,
  connectionLimit: 10,
  queueLimit: 0
});

// Test database connection
async function testConnection() {
  try {
    const connection = await pool.getConnection();
    console.log('Connected to MySQL database successfully!');
    connection.release();
  } catch (err) {
    console.error('Error acquiring database connection:', err.message);
  }
}

testConnection();

// Swagger Configuration
const swaggerOptions = {
  swaggerDefinition: {
    openapi: '3.0.0',
    info: {
      title: 'Jewelry Workshop API',
      version: '1.0.0',
      description: 'واجهة برمجة التطبيقات (API) لإدارة عمليات ورشة الذهب',
    },
    servers: [
      {
        url: `http://localhost:${port}`,
        description: 'خادم التطوير المحلي'
      }
    ],
    tags: [
        { name: 'Workers', description: 'إدارة العمال' },
        { name: 'Customers', description: 'إدارة العملاء' },
        { name: 'Production', description: 'إدارة الإنتاج' },
        { name: 'Inventory', description: 'إدارة المخزون' },
        { name: 'Expenses', description: 'إدارة المصروفات' }
    ]
  },
  apis: ['./server.js'], // Path to the API docs
};
const swaggerDocs = swaggerJSDoc(swaggerOptions);
app.use('/api-docs', swaggerUi.serve, swaggerUi.setup(swaggerDocs));


// =================================================================
// ==                  API Endpoints: Workers                     ==
// =================================================================

/**
 * @swagger
 * /api/workers:
 * get:
 * summary: جلب قائمة بكل العمال
 * tags: [Workers]
 * responses:
 * 200:
 * description: قائمة العمال.
 */
app.get('/api/workers', async (req, res) => {
    try {
        const [rows] = await pool.query('SELECT * FROM workers ORDER BY name');
        res.json(rows);
    } catch (err) {
        res.status(500).json({ error: err.message });
    }
});

/**
 * @swagger
 * /api/workers:
 * post:
 * summary: إنشاء عامل جديد
 * tags: [Workers]
 * requestBody:
 * required: true
 * content:
 * application/json:
 * schema:
 * type: object
 * properties:
 * name: { type: 'string' }
 * nationality: { type: 'string' }
 * start_date: { type: 'string', format: 'date' }
 * salary_egp: { type: 'number' }
 * salary_usd: { type: 'number' }
 * notes: { type: 'string' }
 * responses:
 * 201:
 * description: تم إنشاء العامل بنجاح.
 */
app.post('/api/workers', async (req, res) => {
    try {
        const { name, nationality, start_date, salary_egp, salary_usd, notes } = req.body;
        const [result] = await pool.query(
            'INSERT INTO workers (name, nationality, start_date, salary_egp, salary_usd, notes, balance_gold, balance_egp, balance_usd) VALUES (?, ?, ?, ?, ?, ?, 0, 0, 0)',
            [name, nationality, start_date, salary_egp || 0, salary_usd || 0, notes]
        );
        const [newWorker] = await pool.query('SELECT * FROM workers WHERE id = ?', [result.insertId]);
        res.status(201).json(newWorker[0]);
    } catch (err) {
        res.status(500).json({ error: err.message });
    }
});

// =================================================================
// ==                API Endpoints: Customers                   ==
// =================================================================
/**
 * @swagger
 * /api/customers:
 * get:
 * summary: جلب قائمة بكل العملاء
 * tags: [Customers]
 * responses:
 * 200:
 * description: قائمة العملاء.
 */
app.get('/api/customers', async (req, res) => {
    try {
        const [rows] = await pool.query('SELECT * FROM customers ORDER BY name');
        res.json(rows);
    } catch (err) {
        res.status(500).json({ error: err.message });
    }
});

/**
 * @swagger
 * /api/customers/{id}:
 * get:
 * summary: جلب عميل واحد بواسطة الـ ID
 * tags: [Customers]
 * parameters:
 * - in: path
 * name: id
 * required: true
 * schema:
 * type: integer
 * responses:
 * 200:
 * description: بيانات العميل.
 * 404:
 * description: العميل غير موجود.
 */
app.get('/api/customers/:id', async (req, res) => {
    try {
        const { id } = req.params;
        const result = await pool.query('SELECT * FROM customers WHERE id = $1', [id]);
        if (result.rows.length === 0) {
            return res.status(404).json({ msg: 'Customer not found' });
        }
        res.json(result.rows[0]);
    } catch (err) {
        res.status(500).json({ error: err.message });
    }
});

/**
 * @swagger
 * /api/customers:
 * post:
 * summary: إنشاء عميل جديد مع أرصدة افتتاحية
 * tags: [Customers]
 * requestBody:
 * required: true
 * content:
 * application/json:
 * schema:
 * type: object
 * responses:
 * 201:
 * description: تم إنشاء العميل وحركاته الافتتاحية بنجاح.
 */
app.post('/api/customers', async (req, res) => {
    const client = await pool.connect();
    try {
        await client.query('BEGIN'); // Start transaction
        const { name, contact, balance_gold, balance_labor_egp, balance_labor_usd, balance_diamond_usd, balance_egyptian_gold_usd } = req.body;
        
        const newCustomerRes = await client.query(
            'INSERT INTO customers (name, contact, balance_gold, balance_labor_egp, balance_labor_usd, balance_diamond_usd, balance_egyptian_gold_usd) VALUES ($1, $2, $3, $4, $5, $6, $7) RETURNING *',
            [name, contact, balance_gold || 0, balance_labor_egp || 0, balance_labor_usd || 0, balance_diamond_usd || 0, balance_egyptian_gold_usd || 0]
        );
        const newCustomer = newCustomerRes.rows[0];

        // Add opening balance transactions
        const openingBalances = { balanceGold: newCustomer.balance_gold, balanceLaborEGP: newCustomer.balance_labor_egp, balanceLaborUSD: newCustomer.balance_labor_usd, balanceDiamondUSD: newCustomer.balance_diamond_usd, balanceEgyptianGoldUSD: newCustomer.balance_egyptian_gold_usd };
        const insertPromises = [];
        for (const [balanceType, amount] of Object.entries(openingBalances)) {
            if (amount !== 0) {
                const query = 'INSERT INTO customer_transactions (customer_id, description, type, balance_type, amount, created_at) VALUES ($1, $2, $3, $4, $5, $6)';
                insertPromises.push(client.query(query, [newCustomer.id, "رصيد افتتاحي", "debit", balanceType, amount, new Date()]));
            }
        }
        await Promise.all(insertPromises);
        
        await client.query('COMMIT'); // Commit transaction
        res.status(201).json(newCustomer);
    } catch (err) {
        await client.query('ROLLBACK'); // Rollback transaction on error
        console.error(err.message);
        res.status(500).json({ error: err.message });
    } finally {
        client.release();
    }
});


/**
 * @swagger
 * /api/inventory:
 * post:
 * summary: إضافة مادة خام جديدة
 * tags: [Inventory]
 * requestBody: { required: true, content: { 'application/json': { schema: { type: 'object' } } } }
 * responses: { 201: { description: 'تم إضافة المادة بنجاح.' } }
 */
app.post('/api/inventory', async (req, res) => {
    try {
        const { code, name, unit, stock } = req.body;
        const newMaterial = await pool.query('INSERT INTO raw_materials (code, name, unit, stock) VALUES ($1, $2, $3, $4) RETURNING *', [code, name, unit, stock || 0]);
        res.status(201).json(newMaterial.rows[0]);
    } catch(err) { res.status(500).json({ error: err.message }); }
});

// =================================================================
// ==               Missing CRUD Operations                       ==
// =================================================================

// Workers DELETE
app.delete('/api/workers/:id', async (req, res) => {
    const client = await pool.connect();
    try {
        await client.query('BEGIN');
        const { id } = req.params;
        await client.query('DELETE FROM worker_transactions WHERE worker_id = $1', [id]);
        const result = await client.query('DELETE FROM workers WHERE id = $1', [id]);
        if (result.rowCount === 0) {
            await client.query('ROLLBACK');
            return res.status(404).json({ msg: 'Worker not found' });
        }
        await client.query('COMMIT');
        res.json({ msg: 'Worker deleted successfully' });
    } catch (err) {
        await client.query('ROLLBACK');
        res.status(500).json({ error: err.message });
    } finally {
        client.release();
    }
});

// Customers GET by ID
app.get('/api/customers/:id', async (req, res) => {
    try {
        const { id } = req.params;
        const result = await pool.query('SELECT * FROM customers WHERE id = $1', [id]);
        if (result.rows.length === 0) return res.status(404).json({ msg: 'Customer not found' });
        res.json(result.rows[0]);
    } catch (err) { res.status(500).json({ error: err.message }); }
});

// Customers PUT
app.put('/api/customers/:id', async (req, res) => {
    try {
        const { id } = req.params;
        const { name, contact, balance_gold, balance_labor_egp, balance_labor_usd, balance_diamond_usd, balance_egyptian_gold_usd } = req.body;
        const result = await pool.query('UPDATE customers SET name = $1, contact = $2, balance_gold = $3, balance_labor_egp = $4, balance_labor_usd = $5, balance_diamond_usd = $6, balance_egyptian_gold_usd = $7 WHERE id = $8 RETURNING *', [name, contact, balance_gold, balance_labor_egp, balance_labor_usd, balance_diamond_usd, balance_egyptian_gold_usd, id]);
        if (result.rows.length === 0) return res.status(404).json({ msg: 'Customer not found' });
        res.json(result.rows[0]);
    } catch (err) { res.status(500).json({ error: err.message }); }
});

// Customers DELETE
app.delete('/api/customers/:id', async (req, res) => {
    const client = await pool.connect();
    try {
        await client.query('BEGIN');
        const { id } = req.params;
        await client.query('DELETE FROM customer_transactions WHERE customer_id = $1', [id]);
        const result = await client.query('DELETE FROM customers WHERE id = $1', [id]);
        if (result.rowCount === 0) {
            await client.query('ROLLBACK');
            return res.status(404).json({ msg: 'Customer not found' });
        }
        await client.query('COMMIT');
        res.json({ msg: 'Customer deleted successfully' });
    } catch (err) {
        await client.query('ROLLBACK');
        res.status(500).json({ error: err.message });
    } finally {
        client.release();
    }
});

// Customer Transactions
app.get('/api/customers/:id/transactions', async (req, res) => {
    try {
        const { id } = req.params;
        const result = await pool.query('SELECT * FROM customer_transactions WHERE customer_id = $1 ORDER BY created_at DESC', [id]);
        res.json(result.rows);
    } catch (err) { res.status(500).json({ error: err.message }); }
});

app.post('/api/customers/:id/transactions', async (req, res) => {
    const client = await pool.connect();
    try {
        await client.query('BEGIN');
        const { id } = req.params;
        const { description, type, balance_type, amount } = req.body;
        await client.query('INSERT INTO customer_transactions (customer_id, description, type, balance_type, amount) VALUES ($1, $2, $3, $4, $5)', [id, description, type, balance_type, amount]);
        const updateQuery = `UPDATE customers SET ${balance_type} = ${balance_type} ${type === 'debit' ? '+' : '-'} $1 WHERE id = $2`;
        await client.query(updateQuery, [amount, id]);
        await client.query('COMMIT');
        res.status(201).json({ msg: 'Transaction successful' });
    } catch (err) {
        await client.query('ROLLBACK');
        res.status(500).json({ error: err.message });
    } finally {
        client.release();
    }
});

// Production Orders CRUD
app.get('/api/orders/:id', async (req, res) => {
    try {
        const { id } = req.params;
        const result = await pool.query('SELECT * FROM production_orders WHERE id = $1', [id]);
        if (result.rows.length === 0) return res.status(404).json({ msg: 'Order not found' });
        res.json(result.rows[0]);
    } catch (err) { res.status(500).json({ error: err.message }); }
});

app.put('/api/orders/:id', async (req, res) => {
    try {
        const { id } = req.params;
        const { customer_id, worker_id, item, description, carat, weight, quantity, notes, status } = req.body;
        const result = await pool.query('UPDATE production_orders SET customer_id = $1, worker_id = $2, item = $3, description = $4, carat = $5, weight = $6, quantity = $7, notes = $8, status = $9 WHERE id = $10 RETURNING *', [customer_id, worker_id, item, description, carat, weight, quantity, notes, status, id]);
        if (result.rows.length === 0) return res.status(404).json({ msg: 'Order not found' });
        res.json(result.rows[0]);
    } catch (err) { res.status(500).json({ error: err.message }); }
});

app.delete('/api/orders/:id', async (req, res) => {
    try {
        const { id } = req.params;
        const result = await pool.query('DELETE FROM production_orders WHERE id = $1', [id]);
        if (result.rowCount === 0) return res.status(404).json({ msg: 'Order not found' });
        res.json({ msg: 'Order deleted successfully' });
    } catch (err) { res.status(500).json({ error: err.message }); }
});

// Static Files Support
const path = require('path');
app.use(express.static(path.join(__dirname, '..')));
app.get('/', (req, res) => {
    res.sendFile(path.join(__dirname, '..', 'index-mysql.html'));
});
app.get('/app', (req, res) => {
    res.sendFile(path.join(__dirname, '..', 'index-mysql.html'));
});

// Start the server
app.listen(port, () => {
  console.log(`Server running on port ${port}`);
  console.log(`API Documentation: http://localhost:${port}/api-docs`);
  console.log(`Application: http://localhost:${port}/app`);
});
