{"name": "@develar/schema-utils", "version": "2.6.5", "description": "webpack Validation Utils", "license": "MIT", "repository": "webpack/schema-utils", "author": "webpack Contrib (https://github.com/webpack-contrib)", "homepage": "https://github.com/webpack/schema-utils", "bugs": "https://github.com/webpack/schema-utils/issues", "funding": {"type": "opencollective", "url": "https://opencollective.com/webpack"}, "main": "dist/index.js", "types": "declarations/index.d.ts", "engines": {"node": ">= 8.9.0"}, "scripts": {"start": "npm run build -- -w", "clean": "del-cli dist declarations", "prebuild": "npm run clean", "build:types": "tsc --declaration --emitDeclarationOnly --outDir declarations && prettier \"declarations/**/*.ts\" --write", "build:code": "cross-env NODE_ENV=production babel src -d dist --copy-files", "build": "npm-run-all -p \"build:**\"", "commitlint": "commitlint --from=master", "security": "npm audit", "lint:prettier": "prettier \"{**/*,*}.{js,json,md,yml,css,ts}\" --list-different", "lint:js": "eslint --cache .", "lint:types": "tsc --pretty --noEmit", "lint": "npm-run-all -l -p \"lint:**\"", "test:only": "cross-env NODE_ENV=test jest", "test:watch": "npm run test:only -- --watch", "test:coverage": "npm run test:only -- --collectCoverageFrom=\"src/**/*.js\" --coverage", "pretest": "npm run lint", "test": "npm run test:coverage", "prepare": "npm run build", "release": "standard-version", "defaults": "webpack-defaults"}, "files": ["dist", "declarations"], "dependencies": {"ajv": "^6.12.0", "ajv-keywords": "^3.4.1"}, "devDependencies": {"@babel/cli": "^7.8.3", "@babel/core": "^7.8.3", "@babel/preset-env": "^7.8.3", "@commitlint/cli": "^8.3.5", "@commitlint/config-conventional": "^8.3.4", "@types/json-schema": "^7.0.4", "@webpack-contrib/defaults": "^6.3.0", "@webpack-contrib/eslint-config-webpack": "^3.0.0", "babel-jest": "^24.9.0", "commitlint-azure-pipelines-cli": "^1.0.3", "cross-env": "^6.0.3", "del": "^5.1.0", "del-cli": "^3.0.0", "eslint": "^6.8.0", "eslint-config-prettier": "^6.9.0", "eslint-plugin-import": "^2.20.0", "husky": "^4.0.10", "jest": "^24.9.0", "jest-junit": "^10.0.0", "lint-staged": "^9.5.0", "npm-run-all": "^4.1.5", "prettier": "^1.19.1", "standard-version": "^7.0.1", "typescript": "^3.7.5"}, "keywords": ["webpack"]}