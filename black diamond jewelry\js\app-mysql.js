/**
 * التطبيق الرئيسي - MySQL Version
 * يستخدم API Client للتواصل مع قاعدة البيانات MySQL
 */

class JewelryApp {
    constructor() {
        this.currentPage = 'dashboard';
        this.expensesChart = null;
        this.loadingOverlay = document.getElementById('loading-overlay');
        this.loadingStatus = document.getElementById('loading-status');
        this.connectionStatus = document.getElementById('connection-status');
        
        this.init();
    }

    async init() {
        try {
            this.updateLoadingStatus('اختبار الاتصال بالخادم...');
            
            // اختبار الاتصال بالخادم
            await this.testConnection();
            
            this.updateLoadingStatus('تحميل البيانات...');
            
            // تحميل البيانات الأساسية
            await this.loadAllData();
            
            // إعداد التنقل
            this.setupNavigation();
            
            // إعداد الأحداث
            this.setupEventListeners();
            
            // إخفاء شاشة التحميل
            this.hideLoading();
            
            console.log('✅ تم تحميل التطبيق بنجاح');
            
        } catch (error) {
            console.error('❌ خطأ في تحميل التطبيق:', error);
            this.showError('فشل في تحميل التطبيق: ' + error.message);
        }
    }

    async testConnection() {
        try {
            const tables = await apiClient.getTables();
            console.log('📊 الجداول المتاحة:', tables);
            this.updateConnectionStatus(true);
            return true;
        } catch (error) {
            console.error('❌ فشل الاتصال:', error);
            this.updateConnectionStatus(false);
            throw new Error('لا يمكن الاتصال بالخادم');
        }
    }

    updateConnectionStatus(isConnected) {
        const statusElement = this.connectionStatus;
        const indicator = statusElement.querySelector('.status-indicator');
        
        if (isConnected) {
            indicator.className = 'status-indicator status-online';
            statusElement.innerHTML = '<span class="status-indicator status-online"></span>متصل';
        } else {
            indicator.className = 'status-indicator status-offline';
            statusElement.innerHTML = '<span class="status-indicator status-offline"></span>غير متصل';
        }
    }

    updateLoadingStatus(message) {
        if (this.loadingStatus) {
            this.loadingStatus.textContent = message;
        }
    }

    hideLoading() {
        if (this.loadingOverlay) {
            this.loadingOverlay.classList.add('hidden');
        }
    }

    showError(message) {
        if (this.loadingOverlay) {
            this.loadingOverlay.innerHTML = `
                <div class="text-center">
                    <div class="text-red-500 text-6xl mb-4">⚠️</div>
                    <p class="text-lg font-semibold text-red-600 mb-2">خطأ في النظام</p>
                    <p class="text-sm text-gray-600">${message}</p>
                    <button onclick="location.reload()" class="mt-4 bg-red-500 hover:bg-red-600 text-white font-bold py-2 px-4 rounded">
                        إعادة المحاولة
                    </button>
                </div>
            `;
        }
    }

    async loadAllData() {
        try {
            // تحميل البيانات بشكل متوازي
            const [workers, customers, expenses, orders] = await Promise.all([
                apiClient.getWorkers(),
                apiClient.getCustomers(),
                apiClient.getExpenses(),
                apiClient.getProductionOrders()
            ]);

            // تحديث لوحة التحكم
            this.updateDashboard(workers, customers, expenses, orders);
            
            // تحميل جدول العمال
            this.loadWorkersTable(workers);
            
            console.log('📊 تم تحميل جميع البيانات');
            
        } catch (error) {
            console.error('❌ خطأ في تحميل البيانات:', error);
            throw error;
        }
    }

    updateDashboard(workers, customers, expenses, orders) {
        // تحديث مؤشرات الأداء
        document.getElementById('kpiTotalWorkers').textContent = workers.length;
        document.getElementById('kpiTotalCustomers').textContent = customers.length;
        document.getElementById('kpiTotalOrders').textContent = orders.length;
        
        // حساب إجمالي المصروفات
        const totalExpenses = expenses.reduce((sum, expense) => {
            return sum + (expense.currency === 'EGP' ? expense.amount : expense.amount * 50); // تحويل تقريبي للدولار
        }, 0);
        
        document.getElementById('kpiTotalExpenses').textContent = apiClient.formatCurrency(totalExpenses);
        
        // رسم بياني للمصروفات
        this.createExpensesChart(expenses);
    }

    createExpensesChart(expenses) {
        const ctx = document.getElementById('expensesChart');
        if (!ctx) return;

        // تجميع المصروفات حسب الشهر
        const monthlyExpenses = {};
        expenses.forEach(expense => {
            const month = new Date(expense.created_at).toLocaleDateString('ar-EG', { year: 'numeric', month: 'long' });
            if (!monthlyExpenses[month]) {
                monthlyExpenses[month] = 0;
            }
            monthlyExpenses[month] += expense.currency === 'EGP' ? expense.amount : expense.amount * 50;
        });

        const labels = Object.keys(monthlyExpenses);
        const data = Object.values(monthlyExpenses);

        if (this.expensesChart) {
            this.expensesChart.destroy();
        }

        this.expensesChart = new Chart(ctx, {
            type: 'bar',
            data: {
                labels: labels,
                datasets: [{
                    label: 'المصروفات الشهرية (بالجنيه المصري)',
                    data: data,
                    backgroundColor: 'rgba(239, 68, 68, 0.6)',
                    borderColor: 'rgba(239, 68, 68, 1)',
                    borderWidth: 1,
                    borderRadius: 5,
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true
                    }
                }
            }
        });
    }

    loadWorkersTable(workers) {
        const tableBody = document.getElementById('workersTableBody');
        if (!tableBody) return;

        if (workers.length === 0) {
            tableBody.innerHTML = '<tr><td colspan="5" class="text-center p-4">لم يتم إضافة أي عمال بعد.</td></tr>';
            return;
        }

        tableBody.innerHTML = workers.map(worker => `
            <tr class="bg-white border-b hover:bg-gray-50">
                <td class="px-6 py-4 font-medium">${worker.name}</td>
                <td class="px-6 py-4">${worker.nationality || 'غير محدد'}</td>
                <td class="px-6 py-4 ${apiClient.getBalanceColor(worker.balance_gold)}">${apiClient.formatNumber(worker.balance_gold)}</td>
                <td class="px-6 py-4 ${apiClient.getBalanceColor(worker.balance_egp)}">${apiClient.formatCurrency(worker.balance_egp)}</td>
                <td class="px-6 py-4">
                    <button onclick="app.viewWorkerDetails(${worker.id})" class="text-blue-600 hover:text-blue-800 ml-2">عرض</button>
                    <button onclick="app.editWorker(${worker.id})" class="text-green-600 hover:text-green-800 ml-2">تعديل</button>
                    <button onclick="app.deleteWorker(${worker.id})" class="text-red-600 hover:text-red-800">حذف</button>
                </td>
            </tr>
        `).join('');
    }

    setupNavigation() {
        const navLinks = document.querySelectorAll('.nav-link');
        
        navLinks.forEach(link => {
            link.addEventListener('click', (e) => {
                e.preventDefault();
                const targetPage = link.getAttribute('href').substring(1);
                this.showPage(targetPage);
            });
        });
    }

    showPage(pageId) {
        // إخفاء جميع الصفحات
        document.querySelectorAll('.page-section').forEach(section => {
            section.classList.add('hidden');
        });
        
        // إظهار الصفحة المطلوبة
        const targetPage = document.getElementById(pageId);
        if (targetPage) {
            targetPage.classList.remove('hidden');
            this.currentPage = pageId;
        }
        
        // تحديث التنقل
        document.querySelectorAll('.nav-link').forEach(link => {
            link.classList.remove('bg-[#E6DED5]', 'text-[#554639]', 'font-bold', 'shadow-sm');
            link.classList.add('hover:bg-[#E6DED5]');
        });
        
        const activeLink = document.querySelector(`[href="#${pageId}"]`);
        if (activeLink) {
            activeLink.classList.add('bg-[#E6DED5]', 'text-[#554639]', 'font-bold', 'shadow-sm');
            activeLink.classList.remove('hover:bg-[#E6DED5]');
        }
    }

    setupEventListeners() {
        // زر تحديث البيانات
        const refreshBtn = document.getElementById('refreshDataBtn');
        if (refreshBtn) {
            refreshBtn.addEventListener('click', () => this.refreshData());
        }

        // زر إضافة عامل
        const addWorkerBtn = document.getElementById('addWorkerBtn');
        if (addWorkerBtn) {
            addWorkerBtn.addEventListener('click', () => this.showAddWorkerModal());
        }
    }

    async refreshData() {
        try {
            this.updateLoadingStatus('جار تحديث البيانات...');
            await this.loadAllData();
            console.log('✅ تم تحديث البيانات');
        } catch (error) {
            console.error('❌ خطأ في تحديث البيانات:', error);
            alert('فشل في تحديث البيانات: ' + error.message);
        }
    }

    showAddWorkerModal() {
        // سيتم تنفيذها لاحقاً
        alert('ميزة إضافة عامل ستتوفر قريباً');
    }

    viewWorkerDetails(workerId) {
        // سيتم تنفيذها لاحقاً
        alert(`عرض تفاصيل العامل رقم: ${workerId}`);
    }

    editWorker(workerId) {
        // سيتم تنفيذها لاحقاً
        alert(`تعديل العامل رقم: ${workerId}`);
    }

    async deleteWorker(workerId) {
        if (confirm('هل أنت متأكد من حذف هذا العامل؟')) {
            try {
                await apiClient.deleteWorker(workerId);
                await this.refreshData();
                console.log('✅ تم حذف العامل');
            } catch (error) {
                console.error('❌ خطأ في حذف العامل:', error);
                alert('فشل في حذف العامل: ' + error.message);
            }
        }
    }
}

// تشغيل التطبيق عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', () => {
    window.app = new JewelryApp();
});
