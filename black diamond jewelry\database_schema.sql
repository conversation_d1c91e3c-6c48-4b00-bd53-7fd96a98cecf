CREATE DATABASE `black_diamond_jewelry`;

USE `black_diamond_jewelry`;

-- Drop tables if they exist to start fresh
DROP TABLE IF EXISTS worker_transactions, customer_transactions, production_orders, casting_jobs, polishing_jobs, filing_loss_jobs, general_expenses, workers, customers, raw_materials CASCADE;

-- Table: raw_materials
CREATE TABLE raw_materials (
    id INT AUTO_INCREMENT PRIMARY KEY,
    code VARCHAR(50) UNIQUE NOT NULL,
    name VARCHAR(255) NOT NULL,
    unit VARCHAR(50) NOT NULL,
    stock DECIMAL(10, 2) DEFAULT 0.00,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Table: customers
CREATE TABLE customers (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    contact VARCHAR(255),
    balance_gold DECIMAL(10, 2) DEFAULT 0.00,
    balance_labor_egp DECIMAL(10, 2) DEFAULT 0.00,
    balance_labor_usd DECIMAL(10, 2) DEFAULT 0.00,
    balance_diamond_usd DECIMAL(10, 2) DEFAULT 0.00,
    balance_egyptian_gold_usd DECIMAL(10, 2) DEFAULT 0.00,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Table: workers
CREATE TABLE workers (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    nationality VARCHAR(100),
    start_date DATE,
    salary_egp DECIMAL(10, 2) DEFAULT 0.00,
    salary_usd DECIMAL(10, 2) DEFAULT 0.00,
    notes TEXT,
    balance_gold DECIMAL(10, 2) DEFAULT 0.00,
    balance_egp DECIMAL(10, 2) DEFAULT 0.00,
    balance_usd DECIMAL(10, 2) DEFAULT 0.00,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Table: customer_transactions
CREATE TABLE customer_transactions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    customer_id INT,
    description TEXT NOT NULL,
    type VARCHAR(10) NOT NULL,
    balance_type VARCHAR(50) NOT NULL,
    amount DECIMAL(10, 2) NOT NULL,
    original_amount DECIMAL(10, 2),
    original_currency VARCHAR(10),
    exchange_rate DECIMAL(10, 2),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (customer_id) REFERENCES customers(id) ON DELETE CASCADE
);

-- Table: worker_transactions
CREATE TABLE worker_transactions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    worker_id INT,
    description TEXT NOT NULL,
    type VARCHAR(10) NOT NULL,
    balance_type VARCHAR(50) NOT NULL,
    amount DECIMAL(10, 2) NOT NULL,
    carat INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (worker_id) REFERENCES workers(id) ON DELETE CASCADE
);

-- Table: general_expenses
CREATE TABLE general_expenses (
    id INT AUTO_INCREMENT PRIMARY KEY,
    description TEXT NOT NULL,
    amount DECIMAL(10, 2) NOT NULL,
    currency VARCHAR(10) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Table: production_orders
CREATE TABLE production_orders (
    id INT AUTO_INCREMENT PRIMARY KEY,
    customer_id INT,
    worker_id INT,
    item VARCHAR(255),
    description TEXT NOT NULL,
    carat INT,
    weight DECIMAL(10, 2),
    quantity INT DEFAULT 1,
    notes TEXT,
    status VARCHAR(50) DEFAULT 'جديد',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (customer_id) REFERENCES customers(id),
    FOREIGN KEY (worker_id) REFERENCES workers(id)
);

-- Table: casting_jobs
CREATE TABLE casting_jobs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    description TEXT NOT NULL,
    weight_sent DECIMAL(10, 2) NOT NULL,
    weight_returned DECIMAL(10, 2) NOT NULL,
    loss_calculation VARCHAR(255),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Table: polishing_jobs
CREATE TABLE polishing_jobs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    description TEXT NOT NULL,
    weight_debit DECIMAL(10, 2) DEFAULT 0.00,
    weight_credit DECIMAL(10, 2) DEFAULT 0.00,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Table: filing_loss_jobs
CREATE TABLE filing_loss_jobs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    description TEXT NOT NULL,
    weight DECIMAL(10, 2) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);