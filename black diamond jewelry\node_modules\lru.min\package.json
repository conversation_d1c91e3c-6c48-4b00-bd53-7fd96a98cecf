{"name": "lru.min", "version": "1.1.2", "description": "🔥 An extremely fast and efficient LRU cache for JavaScript with high compatibility (including Browsers) — 6.8KB.", "main": "./lib/index.js", "module": "./lib/index.mjs", "types": "./lib/index.d.ts", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/wellwelwel/lru.min.git"}, "bugs": {"url": "https://github.com/wellwelwel/lru.min/issues"}, "author": "https://github.com/wellwelwel", "funding": {"type": "github", "url": "https://github.com/sponsors/wellwelwel"}, "files": ["browser", "lib"], "engines": {"node": ">=8.0.0", "bun": ">=1.0.0", "deno": ">=1.30.0"}, "scripts": {"benchmark:esm": "cd benchmark && npm ci && node index.mjs", "benchmark:cjs": "cd benchmark && npm ci && node index.cjs", "prebuild": "rm -rf ./browser ./lib", "build:browser": "tsx tools/browserfy.ts", "build:esm": "esbuild src/index.ts --outfile=lib/index.mjs --platform=node --target=node12 --format=esm", "build": "tsc && npm run build:esm && npm run build:browser", "test:node": "poku", "test:bun": "bun poku", "test:deno": "deno run -A npm:poku", "test:coverage": "mcr --import tsx --config mcr.config.ts npm run test:node", "lint": "npx @biomejs/biome lint && prettier --check .", "lint:fix": "npx @biomejs/biome lint --write && prettier --write .github/workflows/*.yml .", "update": "pu minor && npm i && npm audit fix", "postupdate": "npm run lint:fix", "size": "ls -lh lib/index.mjs | awk '{print $5}'"}, "devDependencies": {"@babel/core": "^7.26.9", "@babel/preset-env": "^7.26.9", "@biomejs/biome": "^1.9.4", "@types/babel__core": "^7.20.5", "@types/node": "^22.13.10", "esbuild": "^0.25.0", "monocart-coverage-reports": "2.12.1", "packages-update": "^2.0.0", "poku": "^3.0.1", "prettier": "^3.5.3", "terser": "^5.39.0", "tsx": "^4.19.3", "typescript": "^5.8.2"}, "exports": {".": {"import": {"types": "./lib/index.d.ts", "default": "./lib/index.mjs"}, "require": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}}}, "keywords": ["lru", "cache", "caching", "hash", "node", "nodejs", "bun", "deno", "typescript", "browser", "fast", "lru-cache", "quick-lru"]}